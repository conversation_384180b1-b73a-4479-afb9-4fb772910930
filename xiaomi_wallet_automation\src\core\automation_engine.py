"""
自动化引擎

协调各个模块，执行完整的自动化任务流程。
"""

import asyncio
from datetime import datetime
from typing import Dict, List, Optional

from ..utils.logger import get_logger
from ..models.account import Account<PERSON>anager, XiaomiAccount
from ..models.task_result import TaskResult, TaskResultManager
from ..utils.notification_manager import NotificationManager
from .xiaomi_community import XiaomiCommunityManager
from .xiaomi_wallet import XiaomiWalletManager

logger = get_logger(__name__)


class AutomationEngine:
    """自动化引擎"""
    
    def __init__(self, config: Dict, account_manager: AccountManager, 
                 task_result_manager: TaskResultManager, notification_manager: NotificationManager):
        self.config = config
        self.account_manager = account_manager
        self.task_result_manager = task_result_manager
        self.notification_manager = notification_manager
        
        # 初始化任务管理器
        self.community_manager = XiaomiCommunityManager(config)
        self.wallet_manager = XiaomiWalletManager(config)
        
        # 性能配置
        self.max_concurrent_accounts = config.get('performance', {}).get('max_concurrent_accounts', 3)
        self.request_delay = config.get('performance', {}).get('request_delay', 1)
    
    async def run_all_accounts_tasks(self, accounts: List[XiaomiAccount]) -> List[TaskResult]:
        """运行所有账号的任务"""
        logger.info(f"开始执行 {len(accounts)} 个账号的任务")
        
        # 创建信号量限制并发数
        semaphore = asyncio.Semaphore(self.max_concurrent_accounts)
        
        # 创建任务列表
        tasks = []
        for account in accounts:
            task = self._run_account_tasks_with_semaphore(semaphore, account)
            tasks.append(task)
        
        # 并发执行所有任务
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 处理结果
        valid_results = []
        for i, result in enumerate(results):
            if isinstance(result, Exception):
                logger.error(f"账号 {accounts[i].username} 任务执行异常: {str(result)}")
                # 创建错误结果
                error_result = TaskResult(
                    account_id=accounts[i].username,
                    task_type="all_tasks",
                    start_time=datetime.now()
                )
                error_result.complete(success=False, error_message=str(result))
                valid_results.append(error_result)
            else:
                valid_results.append(result)
        
        logger.info(f"所有账号任务执行完成，共 {len(valid_results)} 个结果")
        return valid_results
    
    async def _run_account_tasks_with_semaphore(self, semaphore: asyncio.Semaphore, account: XiaomiAccount) -> TaskResult:
        """使用信号量限制的账号任务执行"""
        async with semaphore:
            return await self.run_account_tasks(account)
    
    async def run_account_tasks(self, account: XiaomiAccount) -> TaskResult:
        """运行单个账号的所有任务"""
        logger.info(f"开始执行账号 {account.username} 的任务")
        
        # 创建总体任务结果
        overall_result = TaskResult(
            account_id=account.username,
            task_type="all_tasks",
            start_time=datetime.now()
        )
        
        try:
            # 执行社区任务
            if self.config.get('tasks', {}).get('community', {}).get('enabled', True):
                community_result = await self.community_manager.run_daily_tasks(account)
                overall_result.details.extend(community_result.details)
                
                if not community_result.success:
                    overall_result.add_detail("community_tasks", f"社区任务失败: {community_result.error_message}")
                else:
                    overall_result.add_detail("community_tasks", "社区任务完成")
                
                # 保存社区任务结果
                self.task_result_manager.add_result(community_result)
                
                # 更新账号信息
                account.update_task_info(community_result.success)
                if not community_result.success:
                    account.set_error(community_result.error_message)
                else:
                    account.clear_error()
            
            # 等待一段时间再执行钱包任务
            await asyncio.sleep(self.request_delay)
            
            # 执行钱包任务
            if self.config.get('tasks', {}).get('wallet', {}).get('enabled', True):
                wallet_result = await self.wallet_manager.run_wallet_tasks(account)
                overall_result.details.extend(wallet_result.details)
                
                if not wallet_result.success:
                    overall_result.add_detail("wallet_tasks", f"钱包任务失败: {wallet_result.error_message}")
                else:
                    overall_result.add_detail("wallet_tasks", "钱包任务完成")
                
                # 保存钱包任务结果
                self.task_result_manager.add_result(wallet_result)
            
            # 判断总体成功状态
            community_success = not self.config.get('tasks', {}).get('community', {}).get('enabled', True) or \
                              any(detail.task_name == "community_tasks" and "完成" in detail.message for detail in overall_result.details)
            
            wallet_success = not self.config.get('tasks', {}).get('wallet', {}).get('enabled', True) or \
                           any(detail.task_name == "wallet_tasks" and "完成" in detail.message for detail in overall_result.details)
            
            overall_success = community_success and wallet_success
            overall_result.complete(success=overall_success)
            
            # 更新账号信息
            self.account_manager.update_account(account)
            
            # 发送任务完成通知
            await self._send_account_completion_notification(account, overall_result)
            
            logger.info(f"账号 {account.username} 任务执行完成，状态: {'成功' if overall_success else '失败'}")
            
        except Exception as e:
            error_msg = f"账号 {account.username} 任务执行异常: {str(e)}"
            logger.error(error_msg)
            
            overall_result.complete(success=False, error_message=str(e))
            
            # 更新账号错误状态
            account.set_error(str(e))
            self.account_manager.update_account(account)
            
            # 发送错误通知
            await self.notification_manager.send_error_notification(account.username, str(e))
        
        # 保存总体结果
        self.task_result_manager.add_result(overall_result)
        
        return overall_result
    
    async def _send_account_completion_notification(self, account: XiaomiAccount, result: TaskResult):
        """发送账号任务完成通知"""
        try:
            # 构建任务结果数据
            task_results = []
            
            for detail in result.details:
                task_results.append({
                    'task_type': detail.task_name,
                    'success': "完成" in detail.message and "失败" not in detail.message,
                    'message': detail.message,
                    'timestamp': detail.timestamp.isoformat(),
                    'duration': 0  # 详情中没有持续时间信息
                })
            
            # 发送通知
            await self.notification_manager.send_task_completion_notification(
                account.username, 
                task_results
            )
            
        except Exception as e:
            logger.error(f"发送账号完成通知失败: {str(e)}")
    
    def generate_summary_report(self, results: List[TaskResult]) -> Dict:
        """生成汇总报告"""
        total_accounts = len(results)
        successful_accounts = sum(1 for result in results if result.success)
        failed_accounts = total_accounts - successful_accounts
        
        # 统计任务详情
        total_tasks = sum(len(result.details) for result in results)
        successful_tasks = 0
        failed_tasks = 0
        
        for result in results:
            for detail in result.details:
                if "完成" in detail.message and "失败" not in detail.message:
                    successful_tasks += 1
                else:
                    failed_tasks += 1
        
        # 计算平均执行时间
        total_duration = sum(result.duration for result in results)
        average_duration = total_duration / total_accounts if total_accounts > 0 else 0
        
        # 统计会员信息
        active_vip_count = 0
        expiring_vip_count = 0
        
        for result in results:
            for detail in result.details:
                if detail.task_name == "vip_status" and detail.data:
                    vip_info = detail.data
                    if "有效" in str(vip_info) or "活跃" in str(vip_info):
                        active_vip_count += 1
                    elif "即将到期" in str(vip_info) or "到期" in str(vip_info):
                        expiring_vip_count += 1
        
        summary = {
            'timestamp': datetime.now().isoformat(),
            'total_accounts': total_accounts,
            'successful_accounts': successful_accounts,
            'failed_accounts': failed_accounts,
            'success_rate': (successful_accounts / total_accounts * 100) if total_accounts > 0 else 0,
            'total_tasks': total_tasks,
            'successful_tasks': successful_tasks,
            'failed_tasks': failed_tasks,
            'task_success_rate': (successful_tasks / total_tasks * 100) if total_tasks > 0 else 0,
            'average_duration': average_duration,
            'active_vip_count': active_vip_count,
            'expiring_vip_count': expiring_vip_count,
            'results': [result.get_summary() for result in results]
        }
        
        return summary
    
    async def send_summary_notification(self, summary: Dict):
        """发送汇总通知"""
        try:
            await self.notification_manager.send_daily_summary_notification(summary)
        except Exception as e:
            logger.error(f"发送汇总通知失败: {str(e)}")
    
    async def run_vip_status_check(self, accounts: List[XiaomiAccount]) -> List[Dict]:
        """运行会员状态检查"""
        logger.info(f"开始检查 {len(accounts)} 个账号的会员状态")
        
        vip_results = []
        
        for account in accounts:
            try:
                logger.info(f"检查账号 {account.username} 的会员状态")
                
                # 检查会员状态
                vip_info = await self.wallet_manager.check_daily_benefits(account)
                
                vip_results.append({
                    'account': account.username,
                    'vip_info': vip_info,
                    'success': True
                })
                
                # 发送会员状态通知
                await self.notification_manager.send_vip_status_notification(
                    account.username, 
                    vip_info
                )
                
                # 等待一段时间
                await asyncio.sleep(self.request_delay)
                
            except Exception as e:
                logger.error(f"检查账号 {account.username} 会员状态失败: {str(e)}")
                
                vip_results.append({
                    'account': account.username,
                    'vip_info': {},
                    'success': False,
                    'error': str(e)
                })
        
        logger.info(f"会员状态检查完成，共检查 {len(vip_results)} 个账号")
        return vip_results
    
    async def test_notification_services(self) -> Dict:
        """测试通知服务"""
        logger.info("开始测试通知服务")
        
        try:
            # 使用通知管理器的测试功能
            test_results = self.notification_manager.test_notification_services()
            
            logger.info(f"通知服务测试完成: {test_results}")
            return test_results
            
        except Exception as e:
            logger.error(f"测试通知服务失败: {str(e)}")
            return {}
    
    def get_engine_status(self) -> Dict:
        """获取引擎状态"""
        return {
            'config_loaded': bool(self.config),
            'accounts_count': len(self.account_manager.accounts),
            'enabled_accounts_count': len(self.account_manager.get_enabled_accounts()),
            'community_tasks_enabled': self.config.get('tasks', {}).get('community', {}).get('enabled', False),
            'wallet_tasks_enabled': self.config.get('tasks', {}).get('wallet', {}).get('enabled', False),
            'notifications_enabled': self.config.get('notifications', {}).get('enabled', False),
            'max_concurrent_accounts': self.max_concurrent_accounts,
            'request_delay': self.request_delay
        }
