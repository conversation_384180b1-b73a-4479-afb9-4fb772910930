@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

REM 小米钱包视频会员自动化管理软件Windows安装脚本

echo ========================================
echo 小米钱包视频会员自动化管理软件安装脚本
echo ========================================
echo.

REM 检查是否在项目目录中
if not exist "main.py" (
    echo [错误] 请在项目根目录中运行此脚本
    pause
    exit /b 1
)

REM 检查Python是否安装
echo [信息] 检查Python环境...
python --version >nul 2>&1
if errorlevel 1 (
    echo [错误] 未找到Python，请先安装Python 3.8+
    echo 下载地址: https://www.python.org/downloads/
    pause
    exit /b 1
)

REM 检查Python版本
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo [成功] Python版本: %PYTHON_VERSION%

REM 检查pip是否可用
echo [信息] 检查pip...
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo [错误] pip不可用，请重新安装Python并确保勾选"Add Python to PATH"
    pause
    exit /b 1
)
echo [成功] pip检查通过

REM 创建虚拟环境
echo [信息] 创建Python虚拟环境...
if exist "venv" (
    echo [警告] 虚拟环境已存在，跳过创建
) else (
    python -m venv venv
    if errorlevel 1 (
        echo [错误] 创建虚拟环境失败
        pause
        exit /b 1
    )
    echo [成功] 虚拟环境创建完成
)

REM 激活虚拟环境
echo [信息] 激活虚拟环境...
call venv\Scripts\activate.bat
if errorlevel 1 (
    echo [错误] 激活虚拟环境失败
    pause
    exit /b 1
)
echo [成功] 虚拟环境已激活

REM 升级pip
echo [信息] 升级pip...
python -m pip install --upgrade pip
if errorlevel 1 (
    echo [警告] pip升级失败，继续安装...
)

REM 安装Python依赖
echo [信息] 安装Python依赖...
if not exist "requirements.txt" (
    echo [错误] 未找到requirements.txt文件
    pause
    exit /b 1
)

python -m pip install -r requirements.txt
if errorlevel 1 (
    echo [错误] 安装Python依赖失败
    echo [信息] 尝试使用国内镜像源...
    python -m pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/
    if errorlevel 1 (
        echo [错误] 使用镜像源安装依赖也失败
        pause
        exit /b 1
    )
)
echo [成功] Python依赖安装完成

REM 创建配置文件
echo [信息] 设置配置文件...
if not exist "config" mkdir config

if not exist "config\config.yaml" (
    if exist "config\config.example.yaml" (
        copy "config\config.example.yaml" "config\config.yaml" >nul
        echo [成功] 配置文件已创建: config\config.yaml
        echo [警告] 请编辑配置文件并填入必要的参数
    ) else (
        echo [错误] 未找到配置模板文件
        pause
        exit /b 1
    )
) else (
    echo [警告] 配置文件已存在，跳过创建
)

REM 创建必要目录
echo [信息] 创建必要目录...
for %%d in (logs data temp screenshots downloads backup) do (
    if not exist "%%d" (
        mkdir "%%d"
        echo [信息] 创建目录: %%d
    )
)
echo [成功] 目录创建完成

REM 运行基础测试
echo [信息] 运行基础测试...
python -c "import sys; print('Python版本:', sys.version)" >nul 2>&1
if errorlevel 1 (
    echo [错误] Python环境测试失败
    pause
    exit /b 1
)
echo [成功] Python环境测试通过

python -c "import selenium; print('Selenium版本:', selenium.__version__)" >nul 2>&1
if errorlevel 1 (
    echo [错误] Selenium测试失败
    pause
    exit /b 1
)
echo [成功] Selenium测试通过

REM 检查Chrome浏览器
echo [信息] 检查Chrome浏览器...
where chrome >nul 2>&1
if errorlevel 1 (
    where "C:\Program Files\Google\Chrome\Application\chrome.exe" >nul 2>&1
    if errorlevel 1 (
        where "C:\Program Files (x86)\Google\Chrome\Application\chrome.exe" >nul 2>&1
        if errorlevel 1 (
            echo [警告] 未检测到Chrome浏览器，请手动安装
            echo 下载地址: https://www.google.com/chrome/
        ) else (
            echo [成功] Chrome浏览器已安装
        )
    ) else (
        echo [成功] Chrome浏览器已安装
    )
) else (
    echo [成功] Chrome浏览器已安装
)

REM 显示完成信息
echo.
echo [成功] 安装完成！
echo.
echo 下一步操作：
echo 1. 编辑配置文件: config\config.yaml
echo 2. 填入小米账号信息和其他必要配置
echo 3. 运行程序: python main.py
echo.
echo 更多信息请查看文档：
echo - 安装指南: docs\installation.md
echo - 使用教程: docs\usage.md
echo - 配置说明: docs\configuration.md
echo.

REM 询问是否立即运行
set /p choice="是否立即运行程序进行测试？(y/n): "
if /i "%choice%"=="y" (
    echo [信息] 启动程序...
    python main.py --test-config
)

echo 按任意键退出...
pause >nul
