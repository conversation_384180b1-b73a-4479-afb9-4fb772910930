"""
通知相关数据模型

定义通知消息和通知结果的数据结构。
"""

import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, Optional, Any
from dataclasses import dataclass, asdict


class MessageType(Enum):
    """消息类型枚举"""
    TASK_COMPLETION = "task_completion"     # 任务完成
    ERROR = "error"                         # 错误通知
    WARNING = "warning"                     # 警告通知
    INFO = "info"                          # 信息通知
    DAILY_SUMMARY = "daily_summary"        # 每日汇总
    VIP_STATUS = "vip_status"              # 会员状态
    TEST = "test"                          # 测试消息


class Priority(Enum):
    """优先级枚举"""
    LOW = "low"           # 低优先级
    NORMAL = "normal"     # 普通优先级
    HIGH = "high"         # 高优先级
    URGENT = "urgent"     # 紧急优先级


@dataclass
class NotificationMessage:
    """通知消息数据模型"""
    title: str
    content: str
    message_type: str = MessageType.INFO.value
    priority: str = Priority.NORMAL.value
    message_id: str = None
    timestamp: datetime = None
    extra_data: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.message_id is None:
            self.message_id = str(uuid.uuid4())
        
        if self.timestamp is None:
            self.timestamp = datetime.now()
        
        # 确保类型是字符串
        if isinstance(self.message_type, MessageType):
            self.message_type = self.message_type.value
        
        if isinstance(self.priority, Priority):
            self.priority = self.priority.value
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'NotificationMessage':
        """从字典创建通知消息"""
        if 'timestamp' in data and isinstance(data['timestamp'], str):
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        
        return cls(**data)
    
    def get_formatted_content(self) -> str:
        """获取格式化的内容"""
        formatted = f"【{self.title}】\n\n{self.content}"
        
        if self.extra_data:
            formatted += "\n\n--- 详细信息 ---\n"
            for key, value in self.extra_data.items():
                if isinstance(value, dict):
                    formatted += f"{key}:\n"
                    for sub_key, sub_value in value.items():
                        formatted += f"  {sub_key}: {sub_value}\n"
                else:
                    formatted += f"{key}: {value}\n"
        
        return formatted
    
    def is_high_priority(self) -> bool:
        """判断是否为高优先级消息"""
        return self.priority in [Priority.HIGH.value, Priority.URGENT.value]
    
    def should_retry_on_failure(self) -> bool:
        """判断失败时是否应该重试"""
        return self.priority in [Priority.HIGH.value, Priority.URGENT.value]


@dataclass
class NotificationResult:
    """通知发送结果数据模型"""
    service: str
    message_id: str
    success: bool = False
    timestamp: datetime = None
    error_message: Optional[str] = None
    response_data: Optional[Dict] = None
    retry_count: int = 0
    
    def __post_init__(self):
        """初始化后处理"""
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'NotificationResult':
        """从字典创建通知结果"""
        if 'timestamp' in data and isinstance(data['timestamp'], str):
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        
        return cls(**data)
    
    def mark_success(self, response_data: Dict = None):
        """标记为成功"""
        self.success = True
        self.error_message = None
        self.response_data = response_data
        self.timestamp = datetime.now()
    
    def mark_failure(self, error_message: str):
        """标记为失败"""
        self.success = False
        self.error_message = error_message
        self.timestamp = datetime.now()
    
    def increment_retry(self):
        """增加重试次数"""
        self.retry_count += 1


class NotificationTemplate:
    """通知模板类"""
    
    @staticmethod
    def create_task_completion_message(account: str, task_results: list) -> NotificationMessage:
        """创建任务完成通知消息"""
        successful_count = sum(1 for result in task_results if result.get('success', False))
        total_count = len(task_results)
        
        title = f"任务完成通知 - {account}"
        content = f"账号 {account} 的自动化任务已完成\n"
        content += f"成功: {successful_count}/{total_count} 个任务"
        
        return NotificationMessage(
            title=title,
            content=content,
            message_type=MessageType.TASK_COMPLETION.value,
            priority=Priority.NORMAL.value,
            extra_data={
                'account': account,
                'task_results': task_results,
                'success_rate': successful_count / total_count if total_count > 0 else 0
            }
        )
    
    @staticmethod
    def create_error_message(account: str, error: str, details: Dict = None) -> NotificationMessage:
        """创建错误通知消息"""
        title = f"错误通知 - {account}"
        content = f"账号 {account} 发生错误:\n{error}"
        
        return NotificationMessage(
            title=title,
            content=content,
            message_type=MessageType.ERROR.value,
            priority=Priority.HIGH.value,
            extra_data={
                'account': account,
                'error': error,
                'details': details or {}
            }
        )
    
    @staticmethod
    def create_vip_status_message(account: str, vip_info: Dict) -> NotificationMessage:
        """创建会员状态通知消息"""
        title = f"会员状态 - {account}"
        content = f"账号 {account} 的会员状态:\n"
        content += f"状态: {vip_info.get('status', '未知')}\n"
        content += f"到期时间: {vip_info.get('expire_time', '未知')}"
        
        return NotificationMessage(
            title=title,
            content=content,
            message_type=MessageType.VIP_STATUS.value,
            priority=Priority.NORMAL.value,
            extra_data={
                'account': account,
                'vip_info': vip_info
            }
        )
    
    @staticmethod
    def create_daily_summary_message(summary: Dict) -> NotificationMessage:
        """创建每日汇总通知消息"""
        title = "每日汇总报告"
        content = f"今日自动化任务汇总:\n"
        content += f"处理账号: {summary.get('total_accounts', 0)} 个\n"
        content += f"成功账号: {summary.get('successful_accounts', 0)} 个\n"
        content += f"失败账号: {summary.get('failed_accounts', 0)} 个\n"
        content += f"总任务数: {summary.get('total_tasks', 0)} 个"
        
        return NotificationMessage(
            title=title,
            content=content,
            message_type=MessageType.DAILY_SUMMARY.value,
            priority=Priority.NORMAL.value,
            extra_data=summary
        )
    
    @staticmethod
    def create_test_message() -> NotificationMessage:
        """创建测试通知消息"""
        title = "通知测试"
        content = "这是一条测试消息，用于验证通知功能是否正常工作。"
        
        return NotificationMessage(
            title=title,
            content=content,
            message_type=MessageType.TEST.value,
            priority=Priority.LOW.value,
            extra_data={
                'test_time': datetime.now().isoformat(),
                'test_purpose': 'notification_service_verification'
            }
        )


class NotificationFilter:
    """通知过滤器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.min_priority = config.get('min_priority', Priority.NORMAL.value)
        self.enabled_types = config.get('enabled_types', [])
        self.disabled_accounts = config.get('disabled_accounts', [])
        self.quiet_hours = config.get('quiet_hours', {})
    
    def should_send(self, message: NotificationMessage) -> bool:
        """判断是否应该发送通知"""
        # 检查优先级
        if not self._check_priority(message.priority):
            return False
        
        # 检查消息类型
        if not self._check_message_type(message.message_type):
            return False
        
        # 检查账号过滤
        if not self._check_account_filter(message):
            return False
        
        # 检查静默时间
        if not self._check_quiet_hours():
            return False
        
        return True
    
    def _check_priority(self, priority: str) -> bool:
        """检查优先级"""
        priority_levels = {
            Priority.LOW.value: 0,
            Priority.NORMAL.value: 1,
            Priority.HIGH.value: 2,
            Priority.URGENT.value: 3
        }
        
        min_level = priority_levels.get(self.min_priority, 1)
        current_level = priority_levels.get(priority, 1)
        
        return current_level >= min_level
    
    def _check_message_type(self, message_type: str) -> bool:
        """检查消息类型"""
        if not self.enabled_types:
            return True  # 如果没有配置，则允许所有类型
        
        return message_type in self.enabled_types
    
    def _check_account_filter(self, message: NotificationMessage) -> bool:
        """检查账号过滤"""
        if not self.disabled_accounts:
            return True
        
        account = message.extra_data.get('account') if message.extra_data else None
        return account not in self.disabled_accounts
    
    def _check_quiet_hours(self) -> bool:
        """检查静默时间"""
        if not self.quiet_hours.get('enabled', False):
            return True
        
        current_hour = datetime.now().hour
        start_hour = self.quiet_hours.get('start_hour', 22)
        end_hour = self.quiet_hours.get('end_hour', 8)
        
        if start_hour <= end_hour:
            # 同一天内的时间段
            return not (start_hour <= current_hour <= end_hour)
        else:
            # 跨天的时间段
            return not (current_hour >= start_hour or current_hour <= end_hour)
