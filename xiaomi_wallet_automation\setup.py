#!/usr/bin/env python3
"""
小米钱包视频会员自动化管理软件安装脚本
"""

from setuptools import setup, find_packages
from pathlib import Path

# 读取README文件
readme_file = Path(__file__).parent / "README.md"
long_description = readme_file.read_text(encoding="utf-8") if readme_file.exists() else ""

# 读取requirements文件
requirements_file = Path(__file__).parent / "requirements.txt"
requirements = []
if requirements_file.exists():
    requirements = requirements_file.read_text(encoding="utf-8").strip().split("\n")
    requirements = [req.strip() for req in requirements if req.strip() and not req.startswith("#")]

setup(
    name="xiaomi-wallet-automation",
    version="1.0.0",
    author="AI Assistant",
    author_email="<EMAIL>",
    description="小米钱包视频会员自动化管理软件",
    long_description=long_description,
    long_description_content_type="text/markdown",
    url="https://github.com/your-repo/xiaomi-wallet-automation",
    packages=find_packages(),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: End Users/Desktop",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Topic :: Internet :: WWW/HTTP :: Browsers",
        "Topic :: Office/Business :: Financial",
        "Topic :: System :: Monitoring",
        "Topic :: Utilities",
    ],
    python_requires=">=3.8",
    install_requires=requirements,
    extras_require={
        "dev": [
            "pytest>=7.4.0",
            "pytest-asyncio>=0.21.0",
            "black>=23.9.0",
            "flake8>=6.1.0",
            "mypy>=1.5.0",
        ],
        "docs": [
            "sphinx>=7.1.0",
            "sphinx-rtd-theme>=1.3.0",
            "myst-parser>=2.0.0",
        ],
    },
    entry_points={
        "console_scripts": [
            "xiaomi-automation=main:main",
        ],
    },
    include_package_data=True,
    package_data={
        "": [
            "config/*.yaml",
            "config/*.yml",
            "config/*.json",
            "docs/*.md",
            "tests/*.py",
        ],
    },
    zip_safe=False,
    keywords=[
        "xiaomi",
        "automation",
        "wallet",
        "vip",
        "member",
        "selenium",
        "web-automation",
        "task-scheduler",
        "notification",
    ],
    project_urls={
        "Bug Reports": "https://github.com/your-repo/xiaomi-wallet-automation/issues",
        "Source": "https://github.com/your-repo/xiaomi-wallet-automation",
        "Documentation": "https://github.com/your-repo/xiaomi-wallet-automation/blob/main/docs/",
    },
)
