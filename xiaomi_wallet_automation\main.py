#!/usr/bin/env python3
"""
小米钱包视频会员自动化管理软件主程序

Author: AI Assistant
Version: 1.0.0
"""

import asyncio
import sys
import signal
from pathlib import Path
from datetime import datetime
from typing import List

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from src.utils.config_manager import get_config_manager
from src.utils.logger import setup_logging, get_logger
from src.utils.notification_manager import get_notification_manager
from src.models.account import AccountManager
from src.models.task_result import TaskResultManager
from src.core.xiaomi_community import XiaomiCommunityManager
from src.core.xiaomi_wallet import XiaomiWalletManager
from src.core.automation_engine import AutomationEngine

# 全局变量
running = True
logger = None


def signal_handler(signum, frame):
    """信号处理器"""
    global running
    logger.info(f"接收到信号 {signum}，准备退出...")
    running = False


async def main():
    """主函数"""
    global logger
    
    try:
        # 初始化配置管理器
        config_manager = get_config_manager()
        config = config_manager.config_data
        
        # 初始化日志系统
        setup_logging(config)
        logger = get_logger(__name__)
        
        logger.info("=" * 60)
        logger.info("小米钱包视频会员自动化管理软件启动")
        logger.info(f"版本: {config.get('app', {}).get('version', '1.0.0')}")
        logger.info(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        logger.info("=" * 60)
        
        # 注册信号处理器
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
        
        # 初始化各个管理器
        account_manager = AccountManager(config)
        task_result_manager = TaskResultManager()
        notification_manager = get_notification_manager(config)
        
        # 初始化自动化引擎
        automation_engine = AutomationEngine(
            config=config,
            account_manager=account_manager,
            task_result_manager=task_result_manager,
            notification_manager=notification_manager
        )
        
        # 获取启用的账号
        enabled_accounts = account_manager.get_enabled_accounts()
        
        if not enabled_accounts:
            logger.warning("没有找到启用的账号，程序退出")
            return
        
        logger.info(f"找到 {len(enabled_accounts)} 个启用的账号")
        
        # 检查是否启用调度器
        scheduler_config = config.get('scheduler', {})
        if scheduler_config.get('enabled', False):
            logger.info("启动调度器模式")
            await run_scheduler_mode(automation_engine, config)
        else:
            logger.info("启动单次执行模式")
            await run_single_mode(automation_engine, enabled_accounts)
        
    except KeyboardInterrupt:
        logger.info("用户中断程序执行")
    except Exception as e:
        logger.error(f"程序执行过程中发生错误: {str(e)}")
        raise
    finally:
        logger.info("程序退出")


async def run_single_mode(automation_engine: AutomationEngine, accounts: List):
    """单次执行模式"""
    logger.info("开始执行单次任务")
    
    try:
        # 执行所有账号的任务
        results = await automation_engine.run_all_accounts_tasks(accounts)
        
        # 生成汇总报告
        summary = automation_engine.generate_summary_report(results)
        
        logger.info("任务执行完成")
        logger.info(f"总账号数: {summary['total_accounts']}")
        logger.info(f"成功账号: {summary['successful_accounts']}")
        logger.info(f"失败账号: {summary['failed_accounts']}")
        logger.info(f"成功率: {summary['success_rate']:.1f}%")
        
        # 发送汇总通知
        await automation_engine.send_summary_notification(summary)
        
    except Exception as e:
        logger.error(f"单次执行模式发生错误: {str(e)}")
        raise


async def run_scheduler_mode(automation_engine: AutomationEngine, config: dict):
    """调度器模式"""
    from src.core.task_scheduler import TaskScheduler
    
    logger.info("启动调度器模式")
    
    try:
        # 创建任务调度器
        scheduler = TaskScheduler(config, automation_engine)
        
        # 启动调度器
        await scheduler.start()
        
        # 保持运行直到收到停止信号
        while running:
            await asyncio.sleep(1)
        
        # 停止调度器
        await scheduler.stop()
        
    except Exception as e:
        logger.error(f"调度器模式发生错误: {str(e)}")
        raise


def check_dependencies():
    """检查依赖项"""
    try:
        import selenium
        import requests
        import yaml
        import loguru
        import onepush
        logger.info("依赖项检查通过")
        return True
    except ImportError as e:
        print(f"缺少必要的依赖项: {e}")
        print("请运行: pip install -r requirements.txt")
        return False


def check_config():
    """检查配置文件"""
    config_path = Path('config/config.yaml')
    
    if not config_path.exists():
        print("配置文件不存在，正在创建默认配置...")
        
        # 检查示例配置文件
        example_config_path = Path('config/config.example.yaml')
        if example_config_path.exists():
            import shutil
            shutil.copy2(example_config_path, config_path)
            print(f"已从示例配置创建配置文件: {config_path}")
            print("请编辑配置文件并填入必要的参数")
            return False
        else:
            print("示例配置文件也不存在，请检查项目完整性")
            return False
    
    return True


def print_banner():
    """打印程序横幅"""
    banner = """
    ╔══════════════════════════════════════════════════════════════╗
    ║                小米钱包视频会员自动化管理软件                ║
    ║                                                              ║
    ║  功能特性:                                                   ║
    ║  • 小米社区自动化任务 (签到、浏览、互动)                     ║
    ║  • 小米钱包视频会员自动领取                                  ║
    ║  • 多账号管理和批量操作                                      ║
    ║  • 智能验证码识别                                            ║
    ║  • 多种通知推送方式                                          ║
    ║  • 详细日志记录和报告生成                                    ║
    ║                                                              ║
    ║  版本: 1.0.0                                                 ║
    ║  作者: AI Assistant                                          ║
    ╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


if __name__ == "__main__":
    # 打印程序横幅
    print_banner()
    
    # 检查依赖项
    if not check_dependencies():
        sys.exit(1)
    
    # 检查配置文件
    if not check_config():
        sys.exit(1)
    
    # 运行主程序
    try:
        if sys.platform == "win32":
            # Windows系统需要设置事件循环策略
            asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
        
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"程序运行失败: {str(e)}")
        sys.exit(1)
