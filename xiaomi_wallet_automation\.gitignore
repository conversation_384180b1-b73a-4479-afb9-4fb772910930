# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# PyInstaller
*.manifest
*.spec

# Installer logs
pip-log.txt
pip-delete-this-directory.txt

# Unit test / coverage reports
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
cover/

# Translations
*.mo
*.pot

# Django stuff:
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal

# Flask stuff:
instance/
.webassets-cache

# Scrapy stuff:
.scrapy

# Sphinx documentation
docs/_build/

# PyBuilder
.pybuilder/
target/

# Jupyter Notebook
.ipynb_checkpoints

# IPython
profile_default/
ipython_config.py

# pyenv
.python-version

# pipenv
Pipfile.lock

# poetry
poetry.lock

# pdm
.pdm.toml

# PEP 582
__pypackages__/

# Celery stuff
celerybeat-schedule
celerybeat.pid

# SageMath parsed files
*.sage.py

# Environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Spyder project settings
.spyderproject
.spyproject

# Rope project settings
.ropeproject

# mkdocs documentation
/site

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Pyre type checker
.pyre/

# pytype static type analyzer
.pytype/

# Cython debug symbols
cython_debug/

# PyCharm
.idea/

# VS Code
.vscode/

# 项目特定文件
config/config.yaml
config/config.yml
config/config.json

# 数据文件
data/
logs/
temp/
screenshots/
page_sources/
downloads/
backup/

# 浏览器驱动
drivers/
*.exe

# 证书和密钥
*.pem
*.key
*.crt
*.p12

# 敏感信息
secrets/
.secrets
*.secret

# 操作系统
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Windows
*.lnk

# Linux
*~

# 临时文件
*.tmp
*.temp
*.swp
*.swo

# 压缩文件
*.zip
*.tar.gz
*.rar
*.7z

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 配置备份
config_backup_*

# Docker
.dockerignore

# 测试文件
test_output/
test_results/

# 性能分析
*.prof

# 内存转储
*.dump

# 调试文件
debug/
*.debug

# 本地配置
local_config.*
.local

# IDE配置
*.sublime-project
*.sublime-workspace

# 文档生成
docs/build/
docs/source/_build/

# 包管理
node_modules/
package-lock.json
yarn.lock

# 其他
*.bak
*.orig
*.rej
*~
.#*
#*#
