"""
浏览器管理工具

负责浏览器的启动、配置、管理和关闭等功能。
"""

import asyncio
import os
import platform
from typing import Dict, Optional
from pathlib import Path

from selenium import webdriver
from selenium.webdriver.chrome.service import Service as ChromeService
from selenium.webdriver.firefox.service import Service as FirefoxService
from selenium.webdriver.edge.service import Service as EdgeService
from selenium.webdriver.chrome.options import Options as ChromeOptions
from selenium.webdriver.firefox.options import Options as FirefoxOptions
from selenium.webdriver.edge.options import Options as EdgeOptions
from webdriver_manager.chrome import ChromeDriverManager
from webdriver_manager.firefox import GeckoDriverManager
from webdriver_manager.microsoft import EdgeChromiumDriverManager
from fake_useragent import UserAgent

from .logger import get_logger

logger = get_logger(__name__)


class BrowserManager:
    """浏览器管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.driver: Optional[webdriver] = None
        self.browser_type = config.get('type', 'chrome').lower()
        self.headless = config.get('headless', True)
        self.window_size = config.get('window_size', '1920,1080')
        self.user_agent = config.get('user_agent', 'auto')
        self.proxy = config.get('proxy')
        self.chrome_options_list = config.get('chrome_options', [])
        
    async def get_driver(self) -> webdriver:
        """获取浏览器驱动"""
        if self.driver is None:
            self.driver = await self._create_driver()
        return self.driver
    
    async def _create_driver(self) -> webdriver:
        """创建浏览器驱动"""
        logger.info(f"创建 {self.browser_type} 浏览器驱动")
        
        try:
            if self.browser_type == 'chrome':
                return await self._create_chrome_driver()
            elif self.browser_type == 'firefox':
                return await self._create_firefox_driver()
            elif self.browser_type == 'edge':
                return await self._create_edge_driver()
            else:
                raise ValueError(f"不支持的浏览器类型: {self.browser_type}")
                
        except Exception as e:
            logger.error(f"创建浏览器驱动失败: {str(e)}")
            raise
    
    async def _create_chrome_driver(self) -> webdriver.Chrome:
        """创建Chrome驱动"""
        options = ChromeOptions()
        
        # 基础配置
        if self.headless:
            options.add_argument('--headless')
        
        # 窗口大小
        options.add_argument(f'--window-size={self.window_size}')
        
        # User Agent
        if self.user_agent == 'auto':
            ua = UserAgent()
            options.add_argument(f'--user-agent={ua.chrome}')
        elif self.user_agent:
            options.add_argument(f'--user-agent={self.user_agent}')
        
        # 代理设置
        if self.proxy:
            options.add_argument(f'--proxy-server={self.proxy}')
        
        # 自定义Chrome选项
        for option in self.chrome_options_list:
            options.add_argument(option)
        
        # 默认优化选项
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-blink-features=AutomationControlled')
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        
        # 设置下载目录
        download_dir = str(Path.cwd() / 'downloads')
        Path(download_dir).mkdir(exist_ok=True)
        prefs = {
            'download.default_directory': download_dir,
            'download.prompt_for_download': False,
            'download.directory_upgrade': True,
            'safebrowsing.enabled': True
        }
        options.add_experimental_option('prefs', prefs)
        
        # 创建服务
        service = ChromeService(ChromeDriverManager().install())
        
        # 创建驱动
        driver = webdriver.Chrome(service=service, options=options)
        
        # 执行反检测脚本
        driver.execute_script("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})")
        
        return driver
    
    async def _create_firefox_driver(self) -> webdriver.Firefox:
        """创建Firefox驱动"""
        options = FirefoxOptions()
        
        # 基础配置
        if self.headless:
            options.add_argument('--headless')
        
        # 窗口大小
        options.add_argument(f'--width={self.window_size.split(",")[0]}')
        options.add_argument(f'--height={self.window_size.split(",")[1]}')
        
        # User Agent
        if self.user_agent == 'auto':
            ua = UserAgent()
            options.set_preference("general.useragent.override", ua.firefox)
        elif self.user_agent:
            options.set_preference("general.useragent.override", self.user_agent)
        
        # 代理设置
        if self.proxy:
            proxy_host, proxy_port = self.proxy.replace('http://', '').split(':')
            options.set_preference("network.proxy.type", 1)
            options.set_preference("network.proxy.http", proxy_host)
            options.set_preference("network.proxy.http_port", int(proxy_port))
        
        # 禁用图片加载以提高速度
        options.set_preference("permissions.default.image", 2)
        
        # 创建服务
        service = FirefoxService(GeckoDriverManager().install())
        
        # 创建驱动
        driver = webdriver.Firefox(service=service, options=options)
        
        return driver
    
    async def _create_edge_driver(self) -> webdriver.Edge:
        """创建Edge驱动"""
        options = EdgeOptions()
        
        # 基础配置
        if self.headless:
            options.add_argument('--headless')
        
        # 窗口大小
        options.add_argument(f'--window-size={self.window_size}')
        
        # User Agent
        if self.user_agent == 'auto':
            ua = UserAgent()
            options.add_argument(f'--user-agent={ua.chrome}')  # Edge使用Chromium内核
        elif self.user_agent:
            options.add_argument(f'--user-agent={self.user_agent}')
        
        # 代理设置
        if self.proxy:
            options.add_argument(f'--proxy-server={self.proxy}')
        
        # 默认优化选项
        options.add_argument('--no-sandbox')
        options.add_argument('--disable-dev-shm-usage')
        options.add_argument('--disable-gpu')
        options.add_argument('--disable-extensions')
        
        # 创建服务
        service = EdgeService(EdgeChromiumDriverManager().install())
        
        # 创建驱动
        driver = webdriver.Edge(service=service, options=options)
        
        return driver
    
    async def close_driver(self):
        """关闭浏览器驱动"""
        if self.driver:
            try:
                self.driver.quit()
                logger.info("浏览器驱动已关闭")
            except Exception as e:
                logger.error(f"关闭浏览器驱动时发生错误: {str(e)}")
            finally:
                self.driver = None
    
    async def restart_driver(self):
        """重启浏览器驱动"""
        logger.info("重启浏览器驱动")
        await self.close_driver()
        self.driver = await self._create_driver()
        return self.driver
    
    def take_screenshot(self, filename: str = None) -> str:
        """截取屏幕截图"""
        if not self.driver:
            raise RuntimeError("浏览器驱动未初始化")
        
        if filename is None:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"screenshot_{timestamp}.png"
        
        # 确保截图目录存在
        screenshot_dir = Path('screenshots')
        screenshot_dir.mkdir(exist_ok=True)
        
        filepath = screenshot_dir / filename
        self.driver.save_screenshot(str(filepath))
        
        logger.info(f"截图已保存: {filepath}")
        return str(filepath)
    
    def save_page_source(self, filename: str = None) -> str:
        """保存页面源码"""
        if not self.driver:
            raise RuntimeError("浏览器驱动未初始化")
        
        if filename is None:
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"page_source_{timestamp}.html"
        
        # 确保页面源码目录存在
        source_dir = Path('page_sources')
        source_dir.mkdir(exist_ok=True)
        
        filepath = source_dir / filename
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(self.driver.page_source)
        
        logger.info(f"页面源码已保存: {filepath}")
        return str(filepath)
    
    def clear_browser_data(self):
        """清理浏览器数据"""
        if not self.driver:
            return
        
        try:
            # 清理cookies
            self.driver.delete_all_cookies()
            
            # 清理本地存储
            self.driver.execute_script("window.localStorage.clear();")
            self.driver.execute_script("window.sessionStorage.clear();")
            
            logger.info("浏览器数据已清理")
            
        except Exception as e:
            logger.error(f"清理浏览器数据时发生错误: {str(e)}")
    
    def set_page_load_timeout(self, timeout: int = 30):
        """设置页面加载超时时间"""
        if self.driver:
            self.driver.set_page_load_timeout(timeout)
    
    def set_implicit_wait(self, timeout: int = 10):
        """设置隐式等待时间"""
        if self.driver:
            self.driver.implicitly_wait(timeout)
    
    def get_browser_info(self) -> Dict:
        """获取浏览器信息"""
        if not self.driver:
            return {}
        
        try:
            return {
                'browser_name': self.driver.name,
                'browser_version': self.driver.capabilities.get('browserVersion', 'Unknown'),
                'driver_version': self.driver.capabilities.get('chrome', {}).get('chromedriverVersion', 'Unknown'),
                'platform': self.driver.capabilities.get('platformName', 'Unknown'),
                'user_agent': self.driver.execute_script("return navigator.userAgent;")
            }
        except Exception as e:
            logger.error(f"获取浏览器信息失败: {str(e)}")
            return {}


# 全局浏览器管理器实例
_browser_manager: Optional[BrowserManager] = None


def get_browser_manager(config: Dict = None) -> BrowserManager:
    """获取全局浏览器管理器实例"""
    global _browser_manager
    
    if _browser_manager is None and config:
        _browser_manager = BrowserManager(config)
    
    return _browser_manager


async def cleanup_browser():
    """清理全局浏览器实例"""
    global _browser_manager
    
    if _browser_manager:
        await _browser_manager.close_driver()
        _browser_manager = None
