# 使用教程

本文档将详细介绍如何使用小米钱包视频会员自动化管理软件。

## 快速开始

### 1. 基础运行

```bash
# 激活虚拟环境
source venv/bin/activate  # Linux/macOS
# 或
venv\Scripts\activate     # Windows

# 运行程序
python main.py
```

### 2. 命令行参数

```bash
# 显示帮助信息
python main.py --help

# 检查依赖
python main.py --check-deps

# 测试配置
python main.py --test-config

# 调试模式运行
python main.py --debug

# 指定配置文件
python main.py --config /path/to/config.yaml

# 单次运行（不启用调度器）
python main.py --once

# 只运行特定任务
python main.py --task community  # 只运行社区任务
python main.py --task wallet     # 只运行钱包任务

# 只处理特定账号
python main.py --account username1,username2
```

## 功能详解

### 1. 小米社区自动化任务

#### 每日签到
- 自动访问小米社区签到页面
- 完成每日签到任务
- 记录签到状态和奖励

#### 浏览任务
- **用户个人页面浏览**：自动访问和浏览用户个人中心
- **社区帖子浏览**：随机浏览社区热门帖子
- **视频帖子浏览**：观看视频内容，模拟真实用户行为

#### 互动任务
- **点赞帖子**：自动为帖子点赞，每日限制数量
- **关注版块**：关注感兴趣的版块和话题

#### 特殊任务
- **特殊页面浏览**：访问活动页面和特殊功能页面
- **微信小程序签到**：支持微信小程序内的签到功能
- **胡萝卜任务**：完成小米社区的胡萝卜相关任务

#### 成长值统计
- 实时显示当前成长值
- 计算距离Lv5等级所需天数
- 跟踪成长值变化趋势

### 2. 小米钱包视频会员管理

#### 自动领取功能
- 每日自动领取视频会员福利
- 支持多种领取方式和入口
- 智能重试机制

#### 会员状态查询
- 实时查询会员剩余时长
- 监控会员到期时间
- 提供会员状态报告

### 3. 智能化功能

#### 验证码处理
- 集成2captcha服务自动识别验证码
- 支持手动验证码输入
- 验证码预处理和优化

#### 登录管理
- **扫码登录**：支持二维码扫码登录
- **密码登录**：传统用户名密码登录
- **会话保持**：自动保存和恢复登录状态
- **多账号管理**：同时管理多个小米账号

#### 通知推送
- **微信推送**：企业微信应用消息推送
- **钉钉推送**：钉钉群机器人消息推送
- **企业微信推送**：企业微信群机器人推送
- **邮件推送**：SMTP邮件通知

## 配置管理

### 1. 账号配置

```yaml
xiaomi:
  accounts:
    - username: "account1"
      password: "password1"
      phone: "***********"
      enabled: true
    - username: "account2"
      password: "password2"
      phone: "***********"
      enabled: false  # 暂时禁用此账号
```

### 2. 任务配置

```yaml
tasks:
  community:
    enabled: true
    daily_checkin: true
    browse_tasks:
      user_profile: true
      community_posts: true
      video_posts: true
      browse_count: 5  # 每种任务的执行次数
    interaction_tasks:
      like_posts: true
      follow_sections: true
      max_likes_per_day: 10
    special_tasks:
      special_pages: true
      wechat_miniprogram: true
      carrot_tasks: true
  
  wallet:
    enabled: true
    auto_claim_vip: true
    check_vip_status: true
    claim_retry_times: 3
```

### 3. 调度配置

```yaml
scheduler:
  enabled: true
  timezone: "Asia/Shanghai"
  jobs:
    - name: "daily_tasks"
      cron: "0 9 * * *"      # 每天上午9点
      function: "run_daily_tasks"
    - name: "vip_check"
      cron: "0 12,18 * * *"  # 每天12点和18点
      function: "check_vip_status"
    - name: "weekly_summary"
      cron: "0 20 * * 0"     # 每周日晚上8点
      function: "send_weekly_summary"
```

## 运行模式

### 1. 单次执行模式

适用于手动执行或测试：

```bash
python main.py --once
```

特点：
- 立即执行所有启用账号的任务
- 执行完成后程序退出
- 适合手动触发或脚本调用

### 2. 调度器模式

适用于长期自动运行：

```bash
python main.py
```

特点：
- 根据配置的时间表自动执行任务
- 程序持续运行，等待下次执行时间
- 适合服务器部署和无人值守运行

### 3. 调试模式

用于问题排查和开发：

```bash
python main.py --debug
```

特点：
- 详细的日志输出
- 保存页面截图和HTML源码
- 不启用无头浏览器模式

## 日志和监控

### 1. 日志文件

```
logs/
├── main.log          # 主日志文件
├── error.log         # 错误日志
├── task.log          # 任务执行日志
└── notification.log  # 通知推送日志
```

### 2. 日志级别

- **DEBUG**：详细的调试信息
- **INFO**：一般信息和状态
- **WARNING**：警告信息
- **ERROR**：错误信息
- **CRITICAL**：严重错误

### 3. 实时监控

```bash
# 实时查看主日志
tail -f logs/main.log

# 实时查看错误日志
tail -f logs/error.log

# 查看最近的任务执行
tail -n 100 logs/task.log
```

## 数据管理

### 1. 数据文件

```
data/
├── accounts.json           # 账号数据（加密存储）
├── task_results.json       # 任务执行结果
├── notification_history.json  # 通知历史
└── secure_storage.json     # 安全存储数据
```

### 2. 数据备份

```bash
# 手动备份数据
cp -r data/ backup/data_$(date +%Y%m%d_%H%M%S)/

# 自动备份（添加到crontab）
0 2 * * * /path/to/backup_script.sh
```

### 3. 数据恢复

```bash
# 恢复数据
cp -r backup/data_20240101_020000/ data/
```

## 故障排除

### 1. 常见问题

#### 登录失败
- 检查账号密码是否正确
- 确认网络连接正常
- 查看是否需要验证码
- 检查账号是否被限制

#### 任务执行失败
- 查看错误日志了解具体原因
- 检查网页元素是否发生变化
- 确认浏览器驱动版本兼容性

#### 通知发送失败
- 验证通知服务配置
- 检查网络连接和防火墙设置
- 确认API密钥和权限

### 2. 调试技巧

#### 启用调试模式
```bash
python main.py --debug
```

#### 查看浏览器操作
```yaml
browser:
  headless: false  # 显示浏览器窗口
```

#### 保存页面信息
```yaml
development:
  save_screenshots: true
  save_html: true
```

### 3. 性能优化

#### 调整并发数
```yaml
performance:
  max_concurrent_accounts: 2  # 降低并发数
  request_delay: 2           # 增加请求间隔
```

#### 优化浏览器设置
```yaml
browser:
  chrome_options:
    - "--disable-images"      # 禁用图片加载
    - "--disable-javascript"  # 禁用JavaScript（谨慎使用）
```

## 最佳实践

### 1. 安全建议

- 定期更改账号密码
- 使用强密码和双因素认证
- 不要在公共网络上运行
- 定期备份重要数据

### 2. 运行建议

- 设置合理的执行时间间隔
- 监控程序运行状态
- 定期检查日志文件
- 及时更新程序版本

### 3. 维护建议

- 定期清理旧日志文件
- 监控磁盘空间使用
- 检查依赖项更新
- 备份配置文件

## 高级用法

### 1. 自定义任务

可以通过修改源码添加自定义任务：

```python
# 在 xiaomi_community.py 中添加新任务
async def _custom_task(self, driver: webdriver, result: TaskResult):
    """自定义任务"""
    # 实现自定义逻辑
    pass
```

### 2. 扩展通知方式

添加新的通知服务：

```python
# 在 notification_manager.py 中添加新服务
async def _send_custom_service(self, config: Dict, message: NotificationMessage) -> bool:
    """自定义通知服务"""
    # 实现通知逻辑
    pass
```

### 3. API集成

程序提供了丰富的API接口，可以与其他系统集成：

```python
from src.core.automation_engine import AutomationEngine

# 创建引擎实例
engine = AutomationEngine(config, account_manager, task_result_manager, notification_manager)

# 执行特定任务
results = await engine.run_account_tasks(account)
```

## 下一步

- 查看 [API文档](api.md) 了解编程接口
- 阅读 [配置说明](configuration.md) 了解详细配置
- 参考 [故障排除](troubleshooting.md) 解决问题
