"""
基础功能测试

测试程序的基本功能和配置加载。
"""

import pytest
import asyncio
from pathlib import Path
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent.parent))

from src.utils.config_manager import ConfigManager
from src.utils.logger import setup_logging, get_logger
from src.utils.encryption_utils import EncryptionUtils
from src.models.account import XiaomiAccount, AccountManager
from src.models.task_result import TaskResult, TaskDetail


class TestConfigManager:
    """配置管理器测试"""
    
    def test_config_loading(self):
        """测试配置加载"""
        config_manager = ConfigManager('config/config.example.yaml')
        config = config_manager.config_data
        
        assert config is not None
        assert 'app' in config
        assert 'xiaomi' in config
        assert 'tasks' in config
    
    def test_config_get_set(self):
        """测试配置获取和设置"""
        config_manager = ConfigManager('config/config.example.yaml')
        
        # 测试获取配置
        app_name = config_manager.get('app.name')
        assert app_name is not None
        
        # 测试设置配置
        test_value = 'test_value'
        config_manager.set('test.key', test_value)
        assert config_manager.get('test.key') == test_value
    
    def test_config_validation(self):
        """测试配置验证"""
        config_manager = ConfigManager('config/config.example.yaml')
        
        # 基础验证应该通过
        try:
            config_manager._validate_config()
        except Exception as e:
            pytest.fail(f"配置验证失败: {str(e)}")


class TestEncryptionUtils:
    """加密工具测试"""
    
    def test_encryption_decryption(self):
        """测试加密解密"""
        config = {
            'encrypt_passwords': True,
            'encryption_key': 'test_key_12345678901234567890123456'
        }
        
        encryption_utils = EncryptionUtils(config)
        
        # 测试文本加密解密
        original_text = "test_password_123"
        encrypted_text = encryption_utils.encrypt(original_text)
        decrypted_text = encryption_utils.decrypt(encrypted_text)
        
        assert encrypted_text != original_text
        assert decrypted_text == original_text
    
    def test_password_hashing(self):
        """测试密码哈希"""
        config = {'encrypt_passwords': True}
        encryption_utils = EncryptionUtils(config)
        
        password = "test_password"
        hashed = encryption_utils.hash_password(password)
        
        assert hashed != password
        assert encryption_utils.verify_password(password, hashed)
        assert not encryption_utils.verify_password("wrong_password", hashed)


class TestXiaomiAccount:
    """小米账号模型测试"""
    
    def test_account_creation(self):
        """测试账号创建"""
        account = XiaomiAccount(
            username="test_user",
            password="test_password",
            phone="***********"
        )
        
        assert account.username == "test_user"
        assert account.password == "test_password"
        assert account.phone == "***********"
        assert account.enabled is True
        assert account.created_time is not None
    
    def test_account_login_update(self):
        """测试账号登录信息更新"""
        account = XiaomiAccount(
            username="test_user",
            password="test_password",
            phone="***********"
        )
        
        session_data = {"session_id": "test_session"}
        account.update_login_info(session_data)
        
        assert account.session_data == session_data
        assert account.last_login_time is not None
        assert account.login_count == 1
    
    def test_account_task_update(self):
        """测试账号任务信息更新"""
        account = XiaomiAccount(
            username="test_user",
            password="test_password",
            phone="***********"
        )
        
        # 测试成功任务更新
        account.update_task_info(success=True)
        assert account.total_tasks_completed == 1
        assert account.consecutive_success_days == 1
        assert account.error_count == 0
        
        # 测试失败任务更新
        account.update_task_info(success=False)
        assert account.total_tasks_completed == 1
        assert account.consecutive_success_days == 0
        assert account.error_count == 1
    
    def test_account_serialization(self):
        """测试账号序列化"""
        account = XiaomiAccount(
            username="test_user",
            password="test_password",
            phone="***********"
        )
        
        # 测试转换为字典
        account_dict = account.to_dict()
        assert account_dict['username'] == "test_user"
        assert 'password' not in account_dict  # 默认不包含敏感信息
        
        # 测试包含敏感信息的序列化
        account_dict_sensitive = account.to_dict(include_sensitive=True)
        assert account_dict_sensitive['password'] == "test_password"
        
        # 测试从字典创建账号
        new_account = XiaomiAccount.from_dict(account_dict_sensitive)
        assert new_account.username == account.username
        assert new_account.password == account.password


class TestTaskResult:
    """任务结果测试"""
    
    def test_task_result_creation(self):
        """测试任务结果创建"""
        from datetime import datetime
        
        result = TaskResult(
            account_id="test_user",
            task_type="test_task",
            start_time=datetime.now()
        )
        
        assert result.account_id == "test_user"
        assert result.task_type == "test_task"
        assert result.result_id is not None
        assert result.success is False  # 默认为失败
    
    def test_task_detail_addition(self):
        """测试任务详情添加"""
        from datetime import datetime
        
        result = TaskResult(
            account_id="test_user",
            task_type="test_task",
            start_time=datetime.now()
        )
        
        result.add_detail("sub_task", "任务完成", {"key": "value"})
        
        assert len(result.details) == 1
        assert result.details[0].task_name == "sub_task"
        assert result.details[0].message == "任务完成"
        assert result.details[0].data == {"key": "value"}
    
    def test_task_completion(self):
        """测试任务完成"""
        from datetime import datetime
        
        result = TaskResult(
            account_id="test_user",
            task_type="test_task",
            start_time=datetime.now()
        )
        
        result.complete(success=True)
        
        assert result.success is True
        assert result.end_time is not None
        assert result.duration > 0
    
    def test_task_result_serialization(self):
        """测试任务结果序列化"""
        from datetime import datetime
        
        result = TaskResult(
            account_id="test_user",
            task_type="test_task",
            start_time=datetime.now()
        )
        
        result.add_detail("sub_task", "任务完成")
        result.complete(success=True)
        
        # 测试转换为字典
        result_dict = result.to_dict()
        assert result_dict['account_id'] == "test_user"
        assert result_dict['success'] is True
        assert len(result_dict['details']) == 1
        
        # 测试从字典创建结果
        new_result = TaskResult.from_dict(result_dict)
        assert new_result.account_id == result.account_id
        assert new_result.success == result.success
        assert len(new_result.details) == len(result.details)


class TestAccountManager:
    """账号管理器测试"""
    
    def test_account_manager_creation(self):
        """测试账号管理器创建"""
        config = {
            'xiaomi': {
                'accounts': [
                    {
                        'username': 'test_user1',
                        'password': 'test_password1',
                        'phone': '***********',
                        'enabled': True
                    },
                    {
                        'username': 'test_user2',
                        'password': 'test_password2',
                        'phone': '***********',
                        'enabled': False
                    }
                ]
            },
            'security': {}
        }
        
        account_manager = AccountManager(config)
        
        assert len(account_manager.accounts) == 2
        assert account_manager.accounts[0].username == 'test_user1'
        assert account_manager.accounts[1].enabled is False
    
    def test_get_enabled_accounts(self):
        """测试获取启用的账号"""
        config = {
            'xiaomi': {
                'accounts': [
                    {
                        'username': 'test_user1',
                        'password': 'test_password1',
                        'phone': '***********',
                        'enabled': True
                    },
                    {
                        'username': 'test_user2',
                        'password': 'test_password2',
                        'phone': '***********',
                        'enabled': False
                    }
                ]
            },
            'security': {}
        }
        
        account_manager = AccountManager(config)
        enabled_accounts = account_manager.get_enabled_accounts()
        
        assert len(enabled_accounts) == 1
        assert enabled_accounts[0].username == 'test_user1'
    
    def test_get_account_by_username(self):
        """测试根据用户名获取账号"""
        config = {
            'xiaomi': {
                'accounts': [
                    {
                        'username': 'test_user1',
                        'password': 'test_password1',
                        'phone': '***********',
                        'enabled': True
                    }
                ]
            },
            'security': {}
        }
        
        account_manager = AccountManager(config)
        
        account = account_manager.get_account_by_username('test_user1')
        assert account is not None
        assert account.username == 'test_user1'
        
        account = account_manager.get_account_by_username('nonexistent')
        assert account is None


@pytest.mark.asyncio
class TestAsyncFunctions:
    """异步函数测试"""
    
    async def test_async_basic(self):
        """测试基础异步功能"""
        # 这里可以添加异步功能的测试
        await asyncio.sleep(0.1)
        assert True


if __name__ == "__main__":
    # 运行测试
    pytest.main([__file__, "-v"])
