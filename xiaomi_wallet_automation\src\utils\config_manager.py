"""
配置管理工具

负责配置文件的加载、验证、更新和管理。
"""

import os
import yaml
import json
from pathlib import Path
from typing import Dict, Any, Optional, List
from jsonschema import validate, ValidationError

from .logger import get_logger

logger = get_logger(__name__)


class ConfigManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = 'config/config.yaml'):
        self.config_path = Path(config_path)
        self.config_data: Dict[str, Any] = {}
        self.schema_path = Path('config/config_schema.json')
        
        # 确保配置目录存在
        self.config_path.parent.mkdir(exist_ok=True)
        
        # 加载配置
        self.load_config()
    
    def load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            if not self.config_path.exists():
                logger.warning(f"配置文件不存在: {self.config_path}")
                self._create_default_config()
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self.config_data = yaml.safe_load(f) or {}
            
            logger.info(f"配置文件加载成功: {self.config_path}")
            
            # 验证配置
            self._validate_config()
            
            # 处理环境变量覆盖
            self._apply_env_overrides()
            
            return self.config_data
            
        except Exception as e:
            logger.error(f"加载配置文件失败: {str(e)}")
            raise
    
    def save_config(self, config_data: Dict[str, Any] = None) -> bool:
        """保存配置文件"""
        try:
            data_to_save = config_data or self.config_data
            
            # 验证配置
            self._validate_config_data(data_to_save)
            
            # 备份原配置文件
            self._backup_config()
            
            # 保存新配置
            with open(self.config_path, 'w', encoding='utf-8') as f:
                yaml.dump(data_to_save, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            self.config_data = data_to_save
            logger.info(f"配置文件保存成功: {self.config_path}")
            return True
            
        except Exception as e:
            logger.error(f"保存配置文件失败: {str(e)}")
            return False
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self.config_data
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> bool:
        """设置配置值"""
        try:
            keys = key.split('.')
            config = self.config_data
            
            # 导航到目标位置
            for k in keys[:-1]:
                if k not in config:
                    config[k] = {}
                config = config[k]
            
            # 设置值
            config[keys[-1]] = value
            
            logger.info(f"配置项已更新: {key} = {value}")
            return True
            
        except Exception as e:
            logger.error(f"设置配置项失败: {str(e)}")
            return False
    
    def update(self, updates: Dict[str, Any]) -> bool:
        """批量更新配置"""
        try:
            for key, value in updates.items():
                self.set(key, value)
            
            logger.info(f"批量更新配置完成，共更新 {len(updates)} 项")
            return True
            
        except Exception as e:
            logger.error(f"批量更新配置失败: {str(e)}")
            return False
    
    def _create_default_config(self):
        """创建默认配置文件"""
        try:
            # 检查是否存在示例配置文件
            example_config_path = self.config_path.parent / 'config.example.yaml'
            
            if example_config_path.exists():
                # 复制示例配置文件
                import shutil
                shutil.copy2(example_config_path, self.config_path)
                logger.info(f"已从示例配置创建配置文件: {self.config_path}")
            else:
                # 创建基础配置
                default_config = self._get_default_config()
                
                with open(self.config_path, 'w', encoding='utf-8') as f:
                    yaml.dump(default_config, f, default_flow_style=False, allow_unicode=True, indent=2)
                
                logger.info(f"已创建默认配置文件: {self.config_path}")
                
        except Exception as e:
            logger.error(f"创建默认配置文件失败: {str(e)}")
            raise
    
    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'app': {
                'name': '小米钱包自动化管理',
                'version': '1.0.0',
                'debug': False,
                'log_level': 'INFO'
            },
            'xiaomi': {
                'accounts': [],
                'login': {
                    'timeout': 30,
                    'retry_times': 3,
                    'use_qr_login': True,
                    'save_session': True
                }
            },
            'tasks': {
                'community': {
                    'enabled': True,
                    'daily_checkin': True,
                    'browse_tasks': {
                        'user_profile': True,
                        'community_posts': True,
                        'video_posts': True,
                        'browse_count': 5
                    }
                },
                'wallet': {
                    'enabled': True,
                    'auto_claim_vip': True,
                    'check_vip_status': True
                }
            },
            'browser': {
                'type': 'chrome',
                'headless': True,
                'window_size': '1920,1080'
            },
            'notifications': {
                'enabled': False,
                'services': {}
            },
            'logging': {
                'level': 'INFO',
                'files': {
                    'main': 'logs/main.log',
                    'error': 'logs/error.log'
                }
            }
        }
    
    def _validate_config(self):
        """验证配置文件"""
        try:
            if self.schema_path.exists():
                with open(self.schema_path, 'r', encoding='utf-8') as f:
                    schema = json.load(f)
                
                validate(instance=self.config_data, schema=schema)
                logger.info("配置文件验证通过")
            else:
                logger.warning("配置文件模式不存在，跳过验证")
                
        except ValidationError as e:
            logger.error(f"配置文件验证失败: {e.message}")
            raise
        except Exception as e:
            logger.warning(f"配置文件验证时发生错误: {str(e)}")
    
    def _validate_config_data(self, config_data: Dict[str, Any]):
        """验证配置数据"""
        # 基础验证
        required_sections = ['app', 'xiaomi', 'tasks', 'browser']
        
        for section in required_sections:
            if section not in config_data:
                raise ValueError(f"缺少必需的配置节: {section}")
        
        # 验证账号配置
        accounts = config_data.get('xiaomi', {}).get('accounts', [])
        for i, account in enumerate(accounts):
            required_fields = ['username', 'password', 'phone']
            for field in required_fields:
                if field not in account:
                    raise ValueError(f"账号 {i+1} 缺少必需字段: {field}")
    
    def _apply_env_overrides(self):
        """应用环境变量覆盖"""
        env_prefix = 'XIAOMI_AUTO_'
        
        for key, value in os.environ.items():
            if key.startswith(env_prefix):
                config_key = key[len(env_prefix):].lower().replace('_', '.')
                
                # 尝试转换值类型
                converted_value = self._convert_env_value(value)
                
                self.set(config_key, converted_value)
                logger.info(f"环境变量覆盖配置: {config_key} = {converted_value}")
    
    def _convert_env_value(self, value: str) -> Any:
        """转换环境变量值类型"""
        # 布尔值
        if value.lower() in ('true', 'false'):
            return value.lower() == 'true'
        
        # 数字
        try:
            if '.' in value:
                return float(value)
            else:
                return int(value)
        except ValueError:
            pass
        
        # JSON
        try:
            return json.loads(value)
        except json.JSONDecodeError:
            pass
        
        # 字符串
        return value
    
    def _backup_config(self):
        """备份配置文件"""
        try:
            if self.config_path.exists():
                from datetime import datetime
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                backup_path = self.config_path.parent / f"config_backup_{timestamp}.yaml"
                
                import shutil
                shutil.copy2(self.config_path, backup_path)
                
                logger.info(f"配置文件已备份: {backup_path}")
                
                # 清理旧备份（保留最近10个）
                self._cleanup_old_backups()
                
        except Exception as e:
            logger.warning(f"备份配置文件失败: {str(e)}")
    
    def _cleanup_old_backups(self, keep_count: int = 10):
        """清理旧的备份文件"""
        try:
            backup_pattern = "config_backup_*.yaml"
            backup_files = list(self.config_path.parent.glob(backup_pattern))
            
            if len(backup_files) > keep_count:
                # 按修改时间排序
                backup_files.sort(key=lambda x: x.stat().st_mtime)
                
                # 删除多余的备份
                for backup_file in backup_files[:-keep_count]:
                    backup_file.unlink()
                    logger.info(f"删除旧备份文件: {backup_file}")
                    
        except Exception as e:
            logger.warning(f"清理备份文件失败: {str(e)}")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要"""
        return {
            'config_file': str(self.config_path),
            'config_exists': self.config_path.exists(),
            'app_name': self.get('app.name', 'Unknown'),
            'app_version': self.get('app.version', 'Unknown'),
            'debug_mode': self.get('app.debug', False),
            'accounts_count': len(self.get('xiaomi.accounts', [])),
            'browser_type': self.get('browser.type', 'chrome'),
            'notifications_enabled': self.get('notifications.enabled', False),
            'last_modified': self.config_path.stat().st_mtime if self.config_path.exists() else None
        }
    
    def export_config(self, export_path: str, include_sensitive: bool = False) -> bool:
        """导出配置文件"""
        try:
            export_data = self.config_data.copy()
            
            if not include_sensitive:
                # 移除敏感信息
                if 'xiaomi' in export_data and 'accounts' in export_data['xiaomi']:
                    for account in export_data['xiaomi']['accounts']:
                        account.pop('password', None)
                
                # 移除其他敏感配置
                sensitive_keys = [
                    'captcha.api_key',
                    'notifications.services.wechat.corpsecret',
                    'notifications.services.email.password',
                    'security.encryption_key'
                ]
                
                for key in sensitive_keys:
                    self._remove_nested_key(export_data, key)
            
            # 保存导出文件
            export_path = Path(export_path)
            export_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(export_path, 'w', encoding='utf-8') as f:
                yaml.dump(export_data, f, default_flow_style=False, allow_unicode=True, indent=2)
            
            logger.info(f"配置文件已导出: {export_path}")
            return True
            
        except Exception as e:
            logger.error(f"导出配置文件失败: {str(e)}")
            return False
    
    def _remove_nested_key(self, data: Dict, key: str):
        """移除嵌套键"""
        keys = key.split('.')
        current = data
        
        try:
            for k in keys[:-1]:
                current = current[k]
            current.pop(keys[-1], None)
        except (KeyError, TypeError):
            pass
    
    def reload_config(self) -> bool:
        """重新加载配置文件"""
        try:
            self.load_config()
            logger.info("配置文件重新加载成功")
            return True
        except Exception as e:
            logger.error(f"重新加载配置文件失败: {str(e)}")
            return False


# 全局配置管理器实例
_config_manager: Optional[ConfigManager] = None


def get_config_manager(config_path: str = None) -> ConfigManager:
    """获取全局配置管理器实例"""
    global _config_manager
    
    if _config_manager is None:
        _config_manager = ConfigManager(config_path or 'config/config.yaml')
    
    return _config_manager


def get_config() -> Dict[str, Any]:
    """获取配置数据"""
    return get_config_manager().config_data
