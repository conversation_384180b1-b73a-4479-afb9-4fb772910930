# 安装指南

本文档将指导您完成小米钱包视频会员自动化管理软件的安装和配置。

## 系统要求

### 操作系统支持
- Windows 10/11 (x64)
- macOS 10.14+ 
- Linux (Ubuntu 18.04+, CentOS 7+)

### 软件依赖
- Python 3.8+ 
- Chrome/Firefox/Edge 浏览器
- 至少 2GB 可用内存
- 至少 1GB 可用磁盘空间

## 安装步骤

### 1. 安装Python

#### Windows
1. 访问 [Python官网](https://www.python.org/downloads/) 下载Python 3.8+
2. 运行安装程序，确保勾选"Add Python to PATH"
3. 验证安装：打开命令提示符，运行 `python --version`

#### macOS
```bash
# 使用Homebrew安装
brew install python@3.9

# 或者下载官方安装包
# https://www.python.org/downloads/macos/
```

#### Linux (Ubuntu/Debian)
```bash
sudo apt update
sudo apt install python3 python3-pip python3-venv
```

#### Linux (CentOS/RHEL)
```bash
sudo yum install python3 python3-pip
# 或者使用dnf (较新版本)
sudo dnf install python3 python3-pip
```

### 2. 下载项目

#### 方式一：Git克隆（推荐）
```bash
git clone https://github.com/your-repo/xiaomi-wallet-automation.git
cd xiaomi-wallet-automation
```

#### 方式二：下载压缩包
1. 下载项目压缩包
2. 解压到目标目录
3. 进入项目目录

### 3. 创建虚拟环境

```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Windows
venv\Scripts\activate

# macOS/Linux
source venv/bin/activate
```

### 4. 安装依赖

```bash
# 升级pip
pip install --upgrade pip

# 安装项目依赖
pip install -r requirements.txt
```

### 5. 安装浏览器驱动

程序会自动下载和管理浏览器驱动，但您也可以手动安装：

#### Chrome驱动
```bash
# 程序会自动下载，无需手动安装
# 如需手动安装，请访问：
# https://chromedriver.chromium.org/
```

#### Firefox驱动
```bash
# 程序会自动下载，无需手动安装
# 如需手动安装，请访问：
# https://github.com/mozilla/geckodriver/releases
```

### 6. 配置文件设置

```bash
# 复制配置模板
cp config/config.example.yaml config/config.yaml

# 编辑配置文件
# Windows
notepad config/config.yaml

# macOS
open -e config/config.yaml

# Linux
nano config/config.yaml
```

## 配置说明

### 基础配置

编辑 `config/config.yaml` 文件，填入以下必要信息：

```yaml
# 小米账号配置
xiaomi:
  accounts:
    - username: "your_username"
      password: "your_password"
      phone: "your_phone"
      enabled: true

# 验证码配置（可选）
captcha:
  service: "2captcha"
  api_key: "your_2captcha_api_key"

# 通知配置（可选）
notifications:
  enabled: true
  services:
    wechat:
      enabled: false
      corpid: "your_corpid"
      corpsecret: "your_corpsecret"
```

### 高级配置

详细配置说明请参考 [配置说明文档](configuration.md)。

## 验证安装

### 1. 运行测试
```bash
python -m pytest tests/ -v
```

### 2. 检查依赖
```bash
python main.py --check-deps
```

### 3. 测试配置
```bash
python main.py --test-config
```

## 常见问题

### 1. Python版本问题
```bash
# 检查Python版本
python --version

# 如果版本过低，请升级Python
```

### 2. 依赖安装失败
```bash
# 使用国内镜像源
pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/

# 或者使用阿里云镜像
pip install -r requirements.txt -i https://mirrors.aliyun.com/pypi/simple/
```

### 3. 浏览器驱动问题
```bash
# 清除驱动缓存
rm -rf ~/.wdm/

# 重新运行程序，会自动下载驱动
python main.py
```

### 4. 权限问题 (Linux/macOS)
```bash
# 给予执行权限
chmod +x main.py

# 如果需要root权限
sudo python main.py
```

### 5. 防火墙/网络问题
- 确保网络连接正常
- 检查防火墙设置
- 如在企业网络，可能需要配置代理

## 部署到服务器

### 1. 宝塔面板部署

1. 在宝塔面板中安装Python管理器
2. 创建Python项目
3. 上传项目文件
4. 安装依赖
5. 配置定时任务

### 2. 青龙面板部署

1. 进入青龙面板
2. 添加订阅或上传脚本
3. 配置环境变量
4. 设置定时任务

### 3. Docker部署

```bash
# 构建镜像
docker build -t xiaomi-automation .

# 运行容器
docker run -d \
  --name xiaomi-automation \
  -v $(pwd)/config:/app/config \
  -v $(pwd)/logs:/app/logs \
  -v $(pwd)/data:/app/data \
  xiaomi-automation
```

### 4. systemd服务 (Linux)

创建服务文件 `/etc/systemd/system/xiaomi-automation.service`：

```ini
[Unit]
Description=Xiaomi Wallet Automation
After=network.target

[Service]
Type=simple
User=your_user
WorkingDirectory=/path/to/xiaomi-wallet-automation
ExecStart=/path/to/venv/bin/python main.py
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
```

启动服务：
```bash
sudo systemctl daemon-reload
sudo systemctl enable xiaomi-automation
sudo systemctl start xiaomi-automation
```

## 更新程序

### 1. Git更新
```bash
git pull origin main
pip install -r requirements.txt
```

### 2. 手动更新
1. 下载新版本
2. 备份配置文件和数据
3. 替换程序文件
4. 恢复配置文件和数据

## 卸载程序

### 1. 停止服务
```bash
# 如果使用systemd
sudo systemctl stop xiaomi-automation
sudo systemctl disable xiaomi-automation

# 如果使用其他方式，请手动停止进程
```

### 2. 删除文件
```bash
# 删除项目目录
rm -rf /path/to/xiaomi-wallet-automation

# 删除虚拟环境
rm -rf venv
```

### 3. 清理配置
```bash
# 删除systemd服务文件
sudo rm /etc/systemd/system/xiaomi-automation.service
sudo systemctl daemon-reload
```

## 技术支持

如果在安装过程中遇到问题，请：

1. 查看 [常见问题文档](faq.md)
2. 检查 [故障排除指南](troubleshooting.md)
3. 提交 [Issue](https://github.com/your-repo/xiaomi-wallet-automation/issues)

## 下一步

安装完成后，请阅读：
- [使用教程](usage.md)
- [配置说明](configuration.md)
- [API文档](api.md)
