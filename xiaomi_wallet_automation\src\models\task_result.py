"""
任务结果数据模型

定义任务执行结果和详情的数据结构。
"""

import uuid
from datetime import datetime
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict, field


@dataclass
class TaskDetail:
    """任务详情数据模型"""
    task_name: str
    message: str
    timestamp: datetime = None
    data: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        data['timestamp'] = self.timestamp.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'TaskDetail':
        """从字典创建任务详情"""
        if 'timestamp' in data and isinstance(data['timestamp'], str):
            data['timestamp'] = datetime.fromisoformat(data['timestamp'])
        
        return cls(**data)


@dataclass
class TaskResult:
    """任务结果数据模型"""
    account_id: str
    task_type: str
    start_time: datetime
    end_time: Optional[datetime] = None
    success: bool = False
    error_message: Optional[str] = None
    duration: float = 0.0
    details: List[TaskDetail] = field(default_factory=list)
    result_id: str = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.result_id is None:
            self.result_id = str(uuid.uuid4())
    
    def add_detail(self, task_name: str, message: str, data: Dict = None):
        """添加任务详情"""
        detail = TaskDetail(
            task_name=task_name,
            message=message,
            data=data
        )
        self.details.append(detail)
    
    def complete(self, success: bool = True, error_message: str = None):
        """完成任务"""
        self.end_time = datetime.now()
        self.success = success
        self.error_message = error_message
        
        if self.start_time:
            self.duration = (self.end_time - self.start_time).total_seconds()
    
    def to_dict(self) -> Dict:
        """转换为字典"""
        data = asdict(self)
        
        # 处理日期时间
        if data['start_time']:
            data['start_time'] = data['start_time'].isoformat()
        if data['end_time']:
            data['end_time'] = data['end_time'].isoformat()
        
        # 处理详情
        data['details'] = [detail.to_dict() for detail in self.details]
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'TaskResult':
        """从字典创建任务结果"""
        # 处理日期时间
        if 'start_time' in data and isinstance(data['start_time'], str):
            data['start_time'] = datetime.fromisoformat(data['start_time'])
        if 'end_time' in data and isinstance(data['end_time'], str):
            data['end_time'] = datetime.fromisoformat(data['end_time'])
        
        # 处理详情
        details = data.pop('details', [])
        
        result = cls(**data)
        
        # 添加详情
        for detail_data in details:
            result.details.append(TaskDetail.from_dict(detail_data))
        
        return result
    
    def get_summary(self) -> Dict:
        """获取任务摘要"""
        return {
            'account_id': self.account_id,
            'task_type': self.task_type,
            'success': self.success,
            'duration': self.duration,
            'start_time': self.start_time.isoformat() if self.start_time else None,
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'error_message': self.error_message,
            'details_count': len(self.details)
        }
    
    def get_error_summary(self) -> Optional[Dict]:
        """获取错误摘要"""
        if not self.error_message:
            return None
        
        return {
            'account_id': self.account_id,
            'task_type': self.task_type,
            'error_message': self.error_message,
            'time': self.end_time.isoformat() if self.end_time else datetime.now().isoformat()
        }
    
    def merge(self, other: 'TaskResult') -> 'TaskResult':
        """合并另一个任务结果"""
        if self.account_id != other.account_id:
            raise ValueError("无法合并不同账号的任务结果")
        
        # 更新结束时间
        if other.end_time and (not self.end_time or other.end_time > self.end_time):
            self.end_time = other.end_time
        
        # 更新成功状态
        self.success = self.success and other.success
        
        # 更新错误消息
        if other.error_message:
            if self.error_message:
                self.error_message += f"; {other.error_message}"
            else:
                self.error_message = other.error_message
        
        # 更新持续时间
        self.duration += other.duration
        
        # 合并详情
        self.details.extend(other.details)
        
        return self


class TaskResultManager:
    """任务结果管理器"""
    
    def __init__(self, storage_path: str = 'data/task_results.json'):
        self.storage_path = storage_path
        self.results: List[TaskResult] = []
        
        # 确保存储目录存在
        import os
        os.makedirs(os.path.dirname(self.storage_path), exist_ok=True)
        
        # 加载历史结果
        self.load_results()
    
    def load_results(self):
        """加载历史结果"""
        try:
            import json
            import os
            
            if os.path.exists(self.storage_path):
                with open(self.storage_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.results = []
                for result_data in data:
                    self.results.append(TaskResult.from_dict(result_data))
                
                print(f"已加载 {len(self.results)} 个任务结果")
                
        except Exception as e:
            print(f"加载任务结果失败: {str(e)}")
            self.results = []
    
    def save_results(self):
        """保存任务结果"""
        try:
            import json
            
            # 限制保存的结果数量
            max_results = 1000
            if len(self.results) > max_results:
                self.results = self.results[-max_results:]
            
            data = [result.to_dict() for result in self.results]
            
            with open(self.storage_path, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存任务结果失败: {str(e)}")
    
    def add_result(self, result: TaskResult):
        """添加任务结果"""
        self.results.append(result)
        self.save_results()
    
    def get_results_by_account(self, account_id: str) -> List[TaskResult]:
        """获取指定账号的任务结果"""
        return [result for result in self.results if result.account_id == account_id]
    
    def get_results_by_type(self, task_type: str) -> List[TaskResult]:
        """获取指定类型的任务结果"""
        return [result for result in self.results if result.task_type == task_type]
    
    def get_recent_results(self, count: int = 10) -> List[TaskResult]:
        """获取最近的任务结果"""
        return self.results[-count:] if count < len(self.results) else self.results
    
    def get_result_by_id(self, result_id: str) -> Optional[TaskResult]:
        """根据ID获取任务结果"""
        for result in self.results:
            if result.result_id == result_id:
                return result
        return None
    
    def get_success_rate(self, account_id: str = None, task_type: str = None) -> float:
        """获取任务成功率"""
        filtered_results = self.results
        
        if account_id:
            filtered_results = [r for r in filtered_results if r.account_id == account_id]
        
        if task_type:
            filtered_results = [r for r in filtered_results if r.task_type == task_type]
        
        if not filtered_results:
            return 0.0
        
        success_count = sum(1 for r in filtered_results if r.success)
        return success_count / len(filtered_results)
    
    def get_daily_summary(self, date: datetime = None) -> Dict:
        """获取每日汇总"""
        if date is None:
            date = datetime.now()
        
        # 过滤当天的结果
        day_start = datetime(date.year, date.month, date.day)
        day_end = datetime(date.year, date.month, date.day, 23, 59, 59)
        
        daily_results = [
            r for r in self.results 
            if r.start_time and day_start <= r.start_time <= day_end
        ]
        
        # 统计数据
        accounts = set(r.account_id for r in daily_results)
        successful_accounts = set(r.account_id for r in daily_results if r.success)
        
        return {
            'date': date.strftime('%Y-%m-%d'),
            'total_accounts': len(accounts),
            'successful_accounts': len(successful_accounts),
            'failed_accounts': len(accounts) - len(successful_accounts),
            'total_tasks': len(daily_results),
            'successful_tasks': sum(1 for r in daily_results if r.success),
            'failed_tasks': sum(1 for r in daily_results if not r.success),
            'average_duration': sum(r.duration for r in daily_results) / len(daily_results) if daily_results else 0,
            'results': [r.get_summary() for r in daily_results]
        }
    
    def clear_old_results(self, days: int = 30):
        """清理旧的任务结果"""
        cutoff_date = datetime.now() - timedelta(days=days)
        
        self.results = [
            r for r in self.results 
            if r.start_time and r.start_time >= cutoff_date
        ]
        
        self.save_results()
        print(f"已清理 {days} 天前的任务结果，剩余 {len(self.results)} 个结果")


# 导入所需模块
from datetime import timedelta
