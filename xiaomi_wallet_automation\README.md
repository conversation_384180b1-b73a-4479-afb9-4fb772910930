# 小米钱包视频会员自动化管理软件

[![Python Version](https://img.shields.io/badge/python-3.8+-blue.svg)](https://www.python.org/downloads/)
[![License](https://img.shields.io/badge/license-MIT-green.svg)](LICENSE)
[![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)]()

一个功能完整的小米钱包视频会员自动化管理工具，支持小米社区任务自动化、会员管理、多账号操作等功能。

## ✨ 功能特性

### 🎯 核心功能
- **小米社区自动化任务**
  - 每日签到自动完成
  - 浏览用户个人页面、社区帖子、视频帖子
  - 自动点赞帖子和关注版块
  - 特殊页面浏览和微信小程序签到
  - 胡萝卜任务自动完成
  - 成长值统计和Lv5等级进度跟踪

- **小米钱包视频会员管理**
  - 每日自动领取视频会员福利
  - 实时查询会员状态和剩余时长
  - 智能重试机制确保领取成功

- **智能化功能**
  - 集成2captcha服务自动识别验证码
  - 支持扫码登录和密码登录
  - 多账号管理和批量操作
  - 会话保持和自动恢复

- **通知推送系统**
  - 微信企业应用推送
  - 钉钉群机器人通知
  - 企业微信群机器人推送
  - SMTP邮件通知
  - 任务完成和错误通知

- **日志和监控**
  - 详细的任务执行日志
  - 错误追踪和调试信息
  - 任务执行汇总报告
  - 数据统计和分析

### 🔧 技术特性
- **跨平台支持**：Windows、Linux、macOS
- **部署兼容**：宝塔面板、青龙面板、Docker
- **模块化设计**：便于维护和功能扩展
- **错误处理**：完善的重试机制和异常处理
- **任务调度**：支持cron表达式定时任务
- **数据安全**：密码加密存储和安全会话管理

## 🚀 快速开始

### 方式一：自动安装（推荐）

**Linux/macOS:**
```bash
chmod +x scripts/install.sh
./scripts/install.sh
```

**Windows:**
```cmd
scripts\install.bat
```

### 方式二：手动安装

1. **克隆项目**
```bash
git clone https://github.com/your-repo/xiaomi-wallet-automation.git
cd xiaomi-wallet-automation
```

2. **安装依赖**
```bash
# 创建虚拟环境
python -m venv venv

# 激活虚拟环境
# Linux/macOS:
source venv/bin/activate
# Windows:
venv\Scripts\activate

# 安装依赖
pip install -r requirements.txt
```

3. **配置设置**
```bash
# 复制配置模板
cp config/config.example.yaml config/config.yaml

# 编辑配置文件
nano config/config.yaml  # Linux
notepad config/config.yaml  # Windows
```

4. **运行程序**
```bash
python main.py
```

### 方式三：Docker部署

```bash
# 构建并运行
docker-compose up -d

# 查看日志
docker-compose logs -f
```

## 📋 配置说明

### 基础配置示例

```yaml
# 小米账号配置
xiaomi:
  accounts:
    - username: "your_username"
      password: "your_password"
      phone: "your_phone"
      enabled: true

# 任务配置
tasks:
  community:
    enabled: true
    daily_checkin: true
    browse_tasks:
      browse_count: 5
  wallet:
    enabled: true
    auto_claim_vip: true

# 通知配置
notifications:
  enabled: true
  services:
    wechat:
      enabled: true
      corpid: "your_corpid"
      corpsecret: "your_corpsecret"
```

详细配置说明请参考 [配置文档](docs/configuration.md)。

## 📖 使用说明

### 命令行参数

```bash
# 基础运行
python main.py

# 单次执行（不启用调度器）
python main.py --once

# 调试模式
python main.py --debug

# 只运行特定任务
python main.py --task community
python main.py --task wallet

# 指定账号
python main.py --account username1,username2

# 测试配置
python main.py --test-config
```

### 运行模式

1. **单次执行模式**：立即执行所有任务后退出
2. **调度器模式**：根据配置的时间表自动执行任务
3. **调试模式**：详细日志输出，保存截图和页面源码

## 📁 项目结构

```
xiaomi_wallet_automation/
├── main.py                     # 主程序入口
├── requirements.txt            # Python依赖
├── setup.py                   # 安装脚本
├── Dockerfile                 # Docker镜像
├── docker-compose.yml         # Docker编排
├── config/                    # 配置文件
│   ├── config.yaml           # 主配置文件
│   └── config.example.yaml   # 配置模板
├── src/                      # 源代码
│   ├── core/                 # 核心功能模块
│   │   ├── xiaomi_community.py    # 小米社区管理
│   │   ├── xiaomi_wallet.py       # 小米钱包管理
│   │   ├── automation_engine.py   # 自动化引擎
│   │   └── task_scheduler.py      # 任务调度器
│   ├── utils/                # 工具类
│   │   ├── logger.py         # 日志管理
│   │   ├── browser_manager.py     # 浏览器管理
│   │   ├── captcha_solver.py      # 验证码识别
│   │   ├── config_manager.py      # 配置管理
│   │   ├── notification_manager.py # 通知管理
│   │   └── encryption_utils.py    # 加密工具
│   └── models/               # 数据模型
│       ├── account.py        # 账号模型
│       ├── task_result.py    # 任务结果模型
│       └── notification.py   # 通知模型
├── scripts/                  # 安装脚本
│   ├── install.sh           # Linux/macOS安装
│   └── install.bat          # Windows安装
├── docs/                     # 文档
│   ├── installation.md      # 安装指南
│   ├── usage.md            # 使用教程
│   └── configuration.md    # 配置说明
├── tests/                    # 测试文件
│   └── test_basic.py        # 基础测试
├── logs/                     # 日志文件（运行时生成）
├── data/                     # 数据存储（运行时生成）
└── temp/                     # 临时文件（运行时生成）
```

## 🔧 系统要求

### 软件要求
- Python 3.8+
- Chrome/Firefox/Edge 浏览器
- 2GB+ 可用内存
- 1GB+ 可用磁盘空间

### 操作系统支持
- Windows 10/11 (x64)
- macOS 10.14+
- Linux (Ubuntu 18.04+, CentOS 7+)

## 📚 文档

- [📦 安装指南](docs/installation.md) - 详细的安装步骤和环境配置
- [🎮 使用教程](docs/usage.md) - 功能介绍和使用方法
- [⚙️ 配置说明](docs/configuration.md) - 配置文件详细说明
- [🔌 API文档](docs/api.md) - 编程接口文档
- [❓ 常见问题](docs/faq.md) - 常见问题解答
- [🔧 故障排除](docs/troubleshooting.md) - 问题诊断和解决

## 🤝 贡献

欢迎提交Issue和Pull Request来帮助改进项目！

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## ⚠️ 免责声明

本软件仅供学习和研究使用，请遵守相关网站的使用条款和法律法规。使用本软件所产生的任何后果由使用者自行承担。

## 🙏 致谢

- [Selenium](https://selenium.dev/) - Web自动化框架
- [onepush](https://github.com/y1ndan/onepush) - 消息推送库
- [2captcha](https://2captcha.com/) - 验证码识别服务
- [loguru](https://github.com/Delgan/loguru) - 日志记录库

---

如果这个项目对您有帮助，请给个 ⭐ Star 支持一下！
