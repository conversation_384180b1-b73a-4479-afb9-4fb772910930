"""
小米社区自动化任务管理器

负责处理小米社区的各种自动化任务，包括签到、浏览、互动等功能。
"""

import asyncio
import random
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from ..utils.logger import get_logger
from ..utils.browser_manager import BrowserManager
from ..utils.captcha_solver import CaptchaSolver
from ..models.account import XiaomiAccount
from ..models.task_result import TaskResult

logger = get_logger(__name__)


class XiaomiCommunityManager:
    """小米社区自动化任务管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.browser_manager = BrowserManager(config.get('browser', {}))
        self.captcha_solver = CaptchaSolver(config.get('captcha', {}))
        self.base_url = "https://www.mi.com/bbs"
        self.login_url = "https://account.xiaomi.com/pass/serviceLogin"
        
    async def run_daily_tasks(self, account: XiaomiAccount) -> TaskResult:
        """运行每日任务"""
        logger.info(f"开始执行账号 {account.username} 的每日任务")
        
        result = TaskResult(
            account_id=account.username,
            task_type="daily_community_tasks",
            start_time=datetime.now()
        )
        
        try:
            # 启动浏览器
            driver = await self.browser_manager.get_driver()
            
            # 登录账号
            login_success = await self._login_account(driver, account)
            if not login_success:
                result.success = False
                result.error_message = "登录失败"
                return result
            
            # 执行各项任务
            tasks_config = self.config.get('tasks', {}).get('community', {})
            
            if tasks_config.get('daily_checkin', True):
                await self._daily_checkin(driver, result)
            
            if tasks_config.get('browse_tasks', {}).get('user_profile', True):
                await self._browse_user_profile(driver, result)
            
            if tasks_config.get('browse_tasks', {}).get('community_posts', True):
                await self._browse_community_posts(driver, result)
            
            if tasks_config.get('browse_tasks', {}).get('video_posts', True):
                await self._browse_video_posts(driver, result)
            
            if tasks_config.get('interaction_tasks', {}).get('like_posts', True):
                await self._like_posts(driver, result)
            
            if tasks_config.get('interaction_tasks', {}).get('follow_sections', True):
                await self._follow_sections(driver, result)
            
            if tasks_config.get('special_tasks', {}).get('special_pages', True):
                await self._browse_special_pages(driver, result)
            
            if tasks_config.get('special_tasks', {}).get('carrot_tasks', True):
                await self._complete_carrot_tasks(driver, result)
            
            # 获取成长值信息
            await self._get_growth_info(driver, result)
            
            result.success = True
            logger.info(f"账号 {account.username} 每日任务执行完成")
            
        except Exception as e:
            logger.error(f"执行每日任务时发生错误: {str(e)}")
            result.success = False
            result.error_message = str(e)
        
        finally:
            result.end_time = datetime.now()
            result.duration = (result.end_time - result.start_time).total_seconds()
            await self.browser_manager.close_driver()
        
        return result
    
    async def _login_account(self, driver: webdriver, account: XiaomiAccount) -> bool:
        """登录小米账号"""
        try:
            logger.info(f"正在登录账号: {account.username}")
            
            # 访问登录页面
            driver.get(self.login_url)
            await asyncio.sleep(2)
            
            # 检查是否需要扫码登录
            if self.config.get('xiaomi', {}).get('login', {}).get('use_qr_login', True):
                return await self._qr_login(driver, account)
            else:
                return await self._password_login(driver, account)
                
        except Exception as e:
            logger.error(f"登录过程中发生错误: {str(e)}")
            return False
    
    async def _qr_login(self, driver: webdriver, account: XiaomiAccount) -> bool:
        """二维码登录"""
        try:
            # 点击二维码登录按钮
            qr_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CLASS_NAME, "qrcode-login"))
            )
            qr_button.click()
            
            logger.info("请扫描二维码完成登录...")
            
            # 等待登录完成（检查页面跳转）
            for _ in range(60):  # 等待最多60秒
                if "serviceLogin" not in driver.current_url:
                    logger.info("二维码登录成功")
                    return True
                await asyncio.sleep(1)
            
            logger.warning("二维码登录超时")
            return False
            
        except Exception as e:
            logger.error(f"二维码登录失败: {str(e)}")
            return False
    
    async def _password_login(self, driver: webdriver, account: XiaomiAccount) -> bool:
        """密码登录"""
        try:
            # 输入用户名
            username_input = WebDriverWait(driver, 10).until(
                EC.presence_of_element_located((By.ID, "username"))
            )
            username_input.clear()
            username_input.send_keys(account.username)
            
            # 输入密码
            password_input = driver.find_element(By.ID, "pwd")
            password_input.clear()
            password_input.send_keys(account.password)
            
            # 处理验证码
            if await self._handle_captcha(driver):
                # 点击登录按钮
                login_button = driver.find_element(By.ID, "login-button")
                login_button.click()
                
                # 等待登录完成
                await asyncio.sleep(3)
                
                if "serviceLogin" not in driver.current_url:
                    logger.info("密码登录成功")
                    return True
            
            return False
            
        except Exception as e:
            logger.error(f"密码登录失败: {str(e)}")
            return False
    
    async def _handle_captcha(self, driver: webdriver) -> bool:
        """处理验证码"""
        try:
            # 检查是否存在验证码
            captcha_img = driver.find_elements(By.CLASS_NAME, "captcha-img")
            if not captcha_img:
                return True
            
            # 获取验证码图片
            captcha_url = captcha_img[0].get_attribute("src")
            
            # 使用验证码识别服务
            captcha_text = await self.captcha_solver.solve_captcha(captcha_url)
            
            if captcha_text:
                # 输入验证码
                captcha_input = driver.find_element(By.ID, "captCode")
                captcha_input.clear()
                captcha_input.send_keys(captcha_text)
                return True
            
            return False
            
        except NoSuchElementException:
            # 没有验证码
            return True
        except Exception as e:
            logger.error(f"处理验证码时发生错误: {str(e)}")
            return False
    
    async def _daily_checkin(self, driver: webdriver, result: TaskResult):
        """每日签到"""
        try:
            logger.info("执行每日签到任务")
            
            # 访问签到页面
            checkin_url = f"{self.base_url}/plugin/checkin"
            driver.get(checkin_url)
            await asyncio.sleep(2)
            
            # 查找签到按钮
            checkin_button = WebDriverWait(driver, 10).until(
                EC.element_to_be_clickable((By.CLASS_NAME, "checkin-btn"))
            )
            
            if "已签到" not in checkin_button.text:
                checkin_button.click()
                await asyncio.sleep(2)
                result.add_detail("daily_checkin", "签到成功")
                logger.info("每日签到完成")
            else:
                result.add_detail("daily_checkin", "今日已签到")
                logger.info("今日已完成签到")
                
        except Exception as e:
            logger.error(f"每日签到失败: {str(e)}")
            result.add_detail("daily_checkin", f"签到失败: {str(e)}")
    
    async def _browse_user_profile(self, driver: webdriver, result: TaskResult):
        """浏览用户个人页面"""
        try:
            logger.info("执行浏览用户个人页面任务")
            
            # 访问个人中心
            profile_url = f"{self.base_url}/home"
            driver.get(profile_url)
            await asyncio.sleep(3)
            
            # 模拟浏览行为
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
            await asyncio.sleep(2)
            driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            await asyncio.sleep(2)
            
            result.add_detail("browse_user_profile", "浏览个人页面完成")
            logger.info("浏览用户个人页面完成")
            
        except Exception as e:
            logger.error(f"浏览用户个人页面失败: {str(e)}")
            result.add_detail("browse_user_profile", f"浏览失败: {str(e)}")

    async def _browse_community_posts(self, driver: webdriver, result: TaskResult):
        """浏览社区帖子"""
        try:
            logger.info("执行浏览社区帖子任务")

            browse_count = self.config.get('tasks', {}).get('community', {}).get('browse_tasks', {}).get('browse_count', 5)

            # 访问社区首页
            driver.get(self.base_url)
            await asyncio.sleep(2)

            # 获取帖子链接
            post_links = driver.find_elements(By.CSS_SELECTOR, ".post-title a")[:browse_count]

            browsed_count = 0
            for link in post_links:
                try:
                    post_url = link.get_attribute("href")
                    driver.get(post_url)
                    await asyncio.sleep(random.uniform(3, 6))

                    # 模拟阅读行为
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight/3);")
                    await asyncio.sleep(2)
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight*2/3);")
                    await asyncio.sleep(2)

                    browsed_count += 1

                except Exception as e:
                    logger.warning(f"浏览帖子失败: {str(e)}")
                    continue

            result.add_detail("browse_community_posts", f"浏览帖子完成，共浏览 {browsed_count} 个帖子")
            logger.info(f"浏览社区帖子完成，共浏览 {browsed_count} 个帖子")

        except Exception as e:
            logger.error(f"浏览社区帖子失败: {str(e)}")
            result.add_detail("browse_community_posts", f"浏览失败: {str(e)}")

    async def _browse_video_posts(self, driver: webdriver, result: TaskResult):
        """浏览视频帖子"""
        try:
            logger.info("执行浏览视频帖子任务")

            browse_count = self.config.get('tasks', {}).get('community', {}).get('browse_tasks', {}).get('browse_count', 5)

            # 访问视频专区
            video_url = f"{self.base_url}/forum-video"
            driver.get(video_url)
            await asyncio.sleep(2)

            # 获取视频帖子链接
            video_links = driver.find_elements(By.CSS_SELECTOR, ".video-item a")[:browse_count]

            browsed_count = 0
            for link in video_links:
                try:
                    video_url = link.get_attribute("href")
                    driver.get(video_url)
                    await asyncio.sleep(random.uniform(5, 10))  # 视频需要更长观看时间

                    # 模拟观看行为
                    driver.execute_script("window.scrollTo(0, 300);")
                    await asyncio.sleep(3)

                    browsed_count += 1

                except Exception as e:
                    logger.warning(f"浏览视频失败: {str(e)}")
                    continue

            result.add_detail("browse_video_posts", f"浏览视频完成，共浏览 {browsed_count} 个视频")
            logger.info(f"浏览视频帖子完成，共浏览 {browsed_count} 个视频")

        except Exception as e:
            logger.error(f"浏览视频帖子失败: {str(e)}")
            result.add_detail("browse_video_posts", f"浏览失败: {str(e)}")

    async def _like_posts(self, driver: webdriver, result: TaskResult):
        """点赞帖子"""
        try:
            logger.info("执行点赞帖子任务")

            max_likes = self.config.get('tasks', {}).get('community', {}).get('interaction_tasks', {}).get('max_likes_per_day', 10)

            # 访问社区首页
            driver.get(self.base_url)
            await asyncio.sleep(2)

            # 获取帖子链接
            post_links = driver.find_elements(By.CSS_SELECTOR, ".post-title a")[:max_likes]

            liked_count = 0
            for link in post_links:
                try:
                    post_url = link.get_attribute("href")
                    driver.get(post_url)
                    await asyncio.sleep(2)

                    # 查找点赞按钮
                    like_button = driver.find_elements(By.CSS_SELECTOR, ".like-btn, .zan-btn")
                    if like_button and "已赞" not in like_button[0].text:
                        like_button[0].click()
                        await asyncio.sleep(1)
                        liked_count += 1

                except Exception as e:
                    logger.warning(f"点赞帖子失败: {str(e)}")
                    continue

            result.add_detail("like_posts", f"点赞完成，共点赞 {liked_count} 个帖子")
            logger.info(f"点赞帖子完成，共点赞 {liked_count} 个帖子")

        except Exception as e:
            logger.error(f"点赞帖子失败: {str(e)}")
            result.add_detail("like_posts", f"点赞失败: {str(e)}")

    async def _follow_sections(self, driver: webdriver, result: TaskResult):
        """关注版块"""
        try:
            logger.info("执行关注版块任务")

            # 访问版块列表
            sections_url = f"{self.base_url}/forum"
            driver.get(sections_url)
            await asyncio.sleep(2)

            # 查找未关注的版块
            follow_buttons = driver.find_elements(By.CSS_SELECTOR, ".follow-btn:not(.followed)")

            followed_count = 0
            for button in follow_buttons[:3]:  # 最多关注3个版块
                try:
                    button.click()
                    await asyncio.sleep(1)
                    followed_count += 1
                except Exception as e:
                    logger.warning(f"关注版块失败: {str(e)}")
                    continue

            result.add_detail("follow_sections", f"关注版块完成，共关注 {followed_count} 个版块")
            logger.info(f"关注版块完成，共关注 {followed_count} 个版块")

        except Exception as e:
            logger.error(f"关注版块失败: {str(e)}")
            result.add_detail("follow_sections", f"关注失败: {str(e)}")

    async def _browse_special_pages(self, driver: webdriver, result: TaskResult):
        """浏览特殊页面"""
        try:
            logger.info("执行浏览特殊页面任务")

            special_pages = [
                f"{self.base_url}/special/activity",
                f"{self.base_url}/special/events",
                f"{self.base_url}/special/community"
            ]

            browsed_count = 0
            for page_url in special_pages:
                try:
                    driver.get(page_url)
                    await asyncio.sleep(random.uniform(3, 5))

                    # 模拟浏览行为
                    driver.execute_script("window.scrollTo(0, document.body.scrollHeight/2);")
                    await asyncio.sleep(2)

                    browsed_count += 1

                except Exception as e:
                    logger.warning(f"浏览特殊页面失败: {str(e)}")
                    continue

            result.add_detail("browse_special_pages", f"浏览特殊页面完成，共浏览 {browsed_count} 个页面")
            logger.info(f"浏览特殊页面完成，共浏览 {browsed_count} 个页面")

        except Exception as e:
            logger.error(f"浏览特殊页面失败: {str(e)}")
            result.add_detail("browse_special_pages", f"浏览失败: {str(e)}")

    async def _complete_carrot_tasks(self, driver: webdriver, result: TaskResult):
        """完成胡萝卜任务"""
        try:
            logger.info("执行胡萝卜任务")

            # 访问胡萝卜任务页面
            carrot_url = f"{self.base_url}/plugin/carrot"
            driver.get(carrot_url)
            await asyncio.sleep(2)

            # 查找可完成的任务
            task_buttons = driver.find_elements(By.CSS_SELECTOR, ".task-btn:not(.completed)")

            completed_count = 0
            for button in task_buttons:
                try:
                    if "完成" in button.text or "领取" in button.text:
                        button.click()
                        await asyncio.sleep(2)
                        completed_count += 1
                except Exception as e:
                    logger.warning(f"完成胡萝卜任务失败: {str(e)}")
                    continue

            result.add_detail("carrot_tasks", f"胡萝卜任务完成，共完成 {completed_count} 个任务")
            logger.info(f"胡萝卜任务完成，共完成 {completed_count} 个任务")

        except Exception as e:
            logger.error(f"胡萝卜任务失败: {str(e)}")
            result.add_detail("carrot_tasks", f"任务失败: {str(e)}")

    async def _get_growth_info(self, driver: webdriver, result: TaskResult):
        """获取成长值信息"""
        try:
            logger.info("获取成长值信息")

            # 访问个人中心
            profile_url = f"{self.base_url}/home"
            driver.get(profile_url)
            await asyncio.sleep(2)

            # 获取当前成长值
            growth_element = driver.find_elements(By.CSS_SELECTOR, ".growth-value, .exp-value")
            if growth_element:
                current_growth = growth_element[0].text

                # 计算距离Lv5所需天数（假设Lv5需要1000成长值，每天可获得10成长值）
                try:
                    growth_value = int(''.join(filter(str.isdigit, current_growth)))
                    days_to_lv5 = max(0, (1000 - growth_value) // 10)

                    growth_info = f"当前成长值: {growth_value}, 距离Lv5还需: {days_to_lv5} 天"
                    result.add_detail("growth_info", growth_info)
                    logger.info(growth_info)

                except ValueError:
                    result.add_detail("growth_info", f"当前成长值: {current_growth}")
            else:
                result.add_detail("growth_info", "无法获取成长值信息")

        except Exception as e:
            logger.error(f"获取成长值信息失败: {str(e)}")
            result.add_detail("growth_info", f"获取失败: {str(e)}")
