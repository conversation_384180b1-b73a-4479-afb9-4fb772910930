"""
通知推送管理器

集成onepush库实现多种推送通知方式，包括微信、钉钉、企业微信、邮件等。
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, List, Optional, Union
from pathlib import Path

from onepush import notify
from ..utils.logger import NotificationLogger
from ..models.notification import NotificationMessage, NotificationResult

logger = NotificationLogger()


class NotificationManager:
    """通知推送管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.notification_config = config.get('notifications', {})
        self.enabled = self.notification_config.get('enabled', True)
        self.services = self.notification_config.get('services', {})
        
        # 通知历史记录
        self.history_file = Path('data/notification_history.json')
        self.history_file.parent.mkdir(exist_ok=True)
        
    async def send_notification(self, message: NotificationMessage) -> List[NotificationResult]:
        """发送通知消息"""
        if not self.enabled:
            logger.info("通知功能已禁用")
            return []
        
        logger.info(f"开始发送通知: {message.title}")
        
        results = []
        
        # 遍历所有启用的通知服务
        for service_name, service_config in self.services.items():
            if service_config.get('enabled', False):
                result = await self._send_to_service(service_name, service_config, message)
                results.append(result)
        
        # 记录通知历史
        await self._save_notification_history(message, results)
        
        return results
    
    async def _send_to_service(self, service_name: str, service_config: Dict, message: NotificationMessage) -> NotificationResult:
        """发送到指定服务"""
        result = NotificationResult(
            service=service_name,
            message_id=message.message_id,
            timestamp=datetime.now()
        )
        
        try:
            logger.info(f"发送通知到 {service_name}")
            
            if service_name == 'wechat':
                success = await self._send_wechat(service_config, message)
            elif service_name == 'dingtalk':
                success = await self._send_dingtalk(service_config, message)
            elif service_name == 'work_wechat':
                success = await self._send_work_wechat(service_config, message)
            elif service_name == 'email':
                success = await self._send_email(service_config, message)
            else:
                logger.error(f"不支持的通知服务: {service_name}")
                success = False
            
            result.success = success
            if success:
                logger.success(service_name, f"通知发送成功: {message.title}")
            else:
                logger.error(service_name, f"通知发送失败: {message.title}")
                
        except Exception as e:
            error_msg = f"发送通知时发生错误: {str(e)}"
            logger.error(service_name, error_msg)
            result.success = False
            result.error_message = error_msg
        
        return result
    
    async def _send_wechat(self, config: Dict, message: NotificationMessage) -> bool:
        """发送微信通知"""
        try:
            # 使用onepush发送企业微信通知
            notify_config = {
                'corpid': config['corpid'],
                'corpsecret': config['corpsecret'],
                'agentid': config['agentid'],
                'touser': config.get('touser', '@all')
            }
            
            content = f"{message.title}\n\n{message.content}"
            if message.extra_data:
                content += f"\n\n详细信息:\n{json.dumps(message.extra_data, ensure_ascii=False, indent=2)}"
            
            result = notify('wechat_work', **notify_config, text=content)
            return result.get('success', False)
            
        except Exception as e:
            logger.error('wechat', f"微信通知发送失败: {str(e)}")
            return False
    
    async def _send_dingtalk(self, config: Dict, message: NotificationMessage) -> bool:
        """发送钉钉通知"""
        try:
            notify_config = {
                'webhook': config['webhook']
            }
            
            if 'secret' in config:
                notify_config['secret'] = config['secret']
            
            content = f"## {message.title}\n\n{message.content}"
            if message.extra_data:
                content += f"\n\n**详细信息:**\n```json\n{json.dumps(message.extra_data, ensure_ascii=False, indent=2)}\n```"
            
            result = notify('dingtalk', **notify_config, text=content)
            return result.get('success', False)
            
        except Exception as e:
            logger.error('dingtalk', f"钉钉通知发送失败: {str(e)}")
            return False
    
    async def _send_work_wechat(self, config: Dict, message: NotificationMessage) -> bool:
        """发送企业微信群机器人通知"""
        try:
            notify_config = {
                'webhook': config['webhook']
            }
            
            content = f"{message.title}\n\n{message.content}"
            if message.extra_data:
                content += f"\n\n详细信息:\n{json.dumps(message.extra_data, ensure_ascii=False, indent=2)}"
            
            result = notify('wechat_work_webhook', **notify_config, text=content)
            return result.get('success', False)
            
        except Exception as e:
            logger.error('work_wechat', f"企业微信通知发送失败: {str(e)}")
            return False
    
    async def _send_email(self, config: Dict, message: NotificationMessage) -> bool:
        """发送邮件通知"""
        try:
            notify_config = {
                'smtp_server': config['smtp_server'],
                'smtp_port': config['smtp_port'],
                'username': config['username'],
                'password': config['password'],
                'to_emails': config['to_emails']
            }
            
            content = message.content
            if message.extra_data:
                content += f"\n\n详细信息:\n{json.dumps(message.extra_data, ensure_ascii=False, indent=2)}"
            
            result = notify('email', **notify_config, title=message.title, text=content)
            return result.get('success', False)
            
        except Exception as e:
            logger.error('email', f"邮件通知发送失败: {str(e)}")
            return False
    
    async def send_task_completion_notification(self, account_username: str, task_results: List[Dict]) -> List[NotificationResult]:
        """发送任务完成通知"""
        # 统计任务结果
        total_tasks = len(task_results)
        successful_tasks = sum(1 for result in task_results if result.get('success', False))
        failed_tasks = total_tasks - successful_tasks
        
        # 构建通知内容
        title = f"小米自动化任务完成 - {account_username}"
        
        content = f"""
账号: {account_username}
执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

任务统计:
- 总任务数: {total_tasks}
- 成功任务: {successful_tasks}
- 失败任务: {failed_tasks}
- 成功率: {(successful_tasks/total_tasks*100):.1f}%

任务详情:
"""
        
        for result in task_results:
            status = "✅" if result.get('success', False) else "❌"
            task_name = result.get('task_type', '未知任务')
            duration = result.get('duration', 0)
            content += f"{status} {task_name} (耗时: {duration:.1f}秒)\n"
            
            if not result.get('success', False) and result.get('error_message'):
                content += f"   错误: {result['error_message']}\n"
        
        # 添加额外信息
        extra_data = {
            'account': account_username,
            'timestamp': datetime.now().isoformat(),
            'summary': {
                'total': total_tasks,
                'successful': successful_tasks,
                'failed': failed_tasks,
                'success_rate': successful_tasks/total_tasks*100 if total_tasks > 0 else 0
            },
            'details': task_results
        }
        
        message = NotificationMessage(
            title=title,
            content=content.strip(),
            message_type='task_completion',
            priority='normal',
            extra_data=extra_data
        )
        
        return await self.send_notification(message)
    
    async def send_error_notification(self, account_username: str, error_message: str, error_details: Dict = None) -> List[NotificationResult]:
        """发送错误通知"""
        title = f"小米自动化任务错误 - {account_username}"
        
        content = f"""
账号: {account_username}
发生时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

错误信息: {error_message}
"""
        
        extra_data = {
            'account': account_username,
            'timestamp': datetime.now().isoformat(),
            'error_message': error_message,
            'error_details': error_details or {}
        }
        
        message = NotificationMessage(
            title=title,
            content=content.strip(),
            message_type='error',
            priority='high',
            extra_data=extra_data
        )
        
        return await self.send_notification(message)
    
    async def send_daily_summary_notification(self, summary_data: Dict) -> List[NotificationResult]:
        """发送每日汇总通知"""
        title = "小米自动化每日汇总报告"
        
        total_accounts = summary_data.get('total_accounts', 0)
        successful_accounts = summary_data.get('successful_accounts', 0)
        failed_accounts = summary_data.get('failed_accounts', 0)
        
        content = f"""
日期: {datetime.now().strftime('%Y-%m-%d')}

账号统计:
- 总账号数: {total_accounts}
- 成功账号: {successful_accounts}
- 失败账号: {failed_accounts}
- 成功率: {(successful_accounts/total_accounts*100):.1f}%

任务统计:
- 总任务数: {summary_data.get('total_tasks', 0)}
- 成功任务: {summary_data.get('successful_tasks', 0)}
- 失败任务: {summary_data.get('failed_tasks', 0)}

会员状态:
- 有效会员: {summary_data.get('active_vip_count', 0)}
- 即将到期: {summary_data.get('expiring_vip_count', 0)}
"""
        
        message = NotificationMessage(
            title=title,
            content=content.strip(),
            message_type='daily_summary',
            priority='normal',
            extra_data=summary_data
        )
        
        return await self.send_notification(message)
    
    async def send_vip_status_notification(self, account_username: str, vip_info: Dict) -> List[NotificationResult]:
        """发送会员状态通知"""
        title = f"小米会员状态更新 - {account_username}"
        
        content = f"""
账号: {account_username}
检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

会员状态: {vip_info.get('status', '未知')}
到期时间: {vip_info.get('expire_time', '未知')}
剩余天数: {vip_info.get('remaining_days', '未知')}
"""
        
        extra_data = {
            'account': account_username,
            'timestamp': datetime.now().isoformat(),
            'vip_info': vip_info
        }
        
        message = NotificationMessage(
            title=title,
            content=content.strip(),
            message_type='vip_status',
            priority='normal',
            extra_data=extra_data
        )
        
        return await self.send_notification(message)
    
    async def _save_notification_history(self, message: NotificationMessage, results: List[NotificationResult]):
        """保存通知历史"""
        try:
            # 读取现有历史
            history = []
            if self.history_file.exists():
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    history = json.load(f)
            
            # 添加新记录
            record = {
                'message': message.to_dict(),
                'results': [result.to_dict() for result in results],
                'timestamp': datetime.now().isoformat()
            }
            
            history.append(record)
            
            # 保留最近1000条记录
            if len(history) > 1000:
                history = history[-1000:]
            
            # 保存历史
            with open(self.history_file, 'w', encoding='utf-8') as f:
                json.dump(history, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            logger.error('history', f"保存通知历史失败: {str(e)}")
    
    def get_notification_history(self, limit: int = 100) -> List[Dict]:
        """获取通知历史"""
        try:
            if not self.history_file.exists():
                return []
            
            with open(self.history_file, 'r', encoding='utf-8') as f:
                history = json.load(f)
            
            return history[-limit:] if limit > 0 else history
            
        except Exception as e:
            logger.error('history', f"读取通知历史失败: {str(e)}")
            return []
    
    def test_notification_services(self) -> Dict[str, bool]:
        """测试所有通知服务"""
        test_message = NotificationMessage(
            title="小米自动化通知测试",
            content="这是一条测试消息，用于验证通知服务是否正常工作。",
            message_type='test',
            priority='low'
        )
        
        results = {}
        
        for service_name, service_config in self.services.items():
            if service_config.get('enabled', False):
                try:
                    # 这里可以添加同步版本的测试逻辑
                    results[service_name] = True
                except Exception as e:
                    logger.error(service_name, f"测试失败: {str(e)}")
                    results[service_name] = False
            else:
                results[service_name] = False
        
        return results


# 全局通知管理器实例
_notification_manager: Optional[NotificationManager] = None


def get_notification_manager(config: Dict = None) -> NotificationManager:
    """获取全局通知管理器实例"""
    global _notification_manager
    
    if _notification_manager is None and config:
        _notification_manager = NotificationManager(config)
    
    return _notification_manager
