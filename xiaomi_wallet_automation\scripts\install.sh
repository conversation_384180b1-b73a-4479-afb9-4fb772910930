#!/bin/bash

# 小米钱包视频会员自动化管理软件安装脚本
# 支持 Linux 和 macOS 系统

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查操作系统
check_os() {
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        OS="linux"
        if command -v apt-get &> /dev/null; then
            PACKAGE_MANAGER="apt"
        elif command -v yum &> /dev/null; then
            PACKAGE_MANAGER="yum"
        elif command -v dnf &> /dev/null; then
            PACKAGE_MANAGER="dnf"
        else
            log_error "不支持的Linux发行版"
            exit 1
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        OS="macos"
        PACKAGE_MANAGER="brew"
    else
        log_error "不支持的操作系统: $OSTYPE"
        exit 1
    fi
    
    log_info "检测到操作系统: $OS"
}

# 检查Python版本
check_python() {
    if command -v python3 &> /dev/null; then
        PYTHON_VERSION=$(python3 -c 'import sys; print(".".join(map(str, sys.version_info[:2])))')
        PYTHON_MAJOR=$(echo $PYTHON_VERSION | cut -d. -f1)
        PYTHON_MINOR=$(echo $PYTHON_VERSION | cut -d. -f2)
        
        if [[ $PYTHON_MAJOR -eq 3 && $PYTHON_MINOR -ge 8 ]]; then
            log_success "Python版本检查通过: $PYTHON_VERSION"
            PYTHON_CMD="python3"
        else
            log_error "Python版本过低，需要3.8+，当前版本: $PYTHON_VERSION"
            exit 1
        fi
    else
        log_error "未找到Python3，请先安装Python 3.8+"
        exit 1
    fi
}

# 安装系统依赖
install_system_deps() {
    log_info "安装系统依赖..."
    
    case $PACKAGE_MANAGER in
        "apt")
            sudo apt update
            sudo apt install -y wget curl unzip python3-pip python3-venv
            ;;
        "yum")
            sudo yum install -y wget curl unzip python3-pip
            ;;
        "dnf")
            sudo dnf install -y wget curl unzip python3-pip
            ;;
        "brew")
            if ! command -v brew &> /dev/null; then
                log_error "请先安装Homebrew: https://brew.sh/"
                exit 1
            fi
            brew install wget curl python@3.9
            ;;
    esac
    
    log_success "系统依赖安装完成"
}

# 安装Chrome浏览器
install_chrome() {
    log_info "检查Chrome浏览器..."
    
    if command -v google-chrome &> /dev/null || command -v chromium-browser &> /dev/null; then
        log_success "Chrome浏览器已安装"
        return
    fi
    
    case $OS in
        "linux")
            case $PACKAGE_MANAGER in
                "apt")
                    wget -q -O - https://dl-ssl.google.com/linux/linux_signing_key.pub | sudo apt-key add -
                    echo "deb [arch=amd64] http://dl.google.com/linux/chrome/deb/ stable main" | sudo tee /etc/apt/sources.list.d/google.list
                    sudo apt update
                    sudo apt install -y google-chrome-stable
                    ;;
                "yum"|"dnf")
                    sudo $PACKAGE_MANAGER install -y google-chrome-stable
                    ;;
            esac
            ;;
        "macos")
            log_warning "请手动安装Chrome浏览器: https://www.google.com/chrome/"
            ;;
    esac
}

# 创建虚拟环境
create_venv() {
    log_info "创建Python虚拟环境..."
    
    if [[ -d "venv" ]]; then
        log_warning "虚拟环境已存在，跳过创建"
    else
        $PYTHON_CMD -m venv venv
        log_success "虚拟环境创建完成"
    fi
}

# 激活虚拟环境
activate_venv() {
    log_info "激活虚拟环境..."
    source venv/bin/activate
    log_success "虚拟环境已激活"
}

# 安装Python依赖
install_python_deps() {
    log_info "安装Python依赖..."
    
    # 升级pip
    pip install --upgrade pip
    
    # 安装依赖
    if [[ -f "requirements.txt" ]]; then
        pip install -r requirements.txt
        log_success "Python依赖安装完成"
    else
        log_error "未找到requirements.txt文件"
        exit 1
    fi
}

# 创建配置文件
setup_config() {
    log_info "设置配置文件..."
    
    if [[ ! -d "config" ]]; then
        mkdir -p config
    fi
    
    if [[ ! -f "config/config.yaml" ]]; then
        if [[ -f "config/config.example.yaml" ]]; then
            cp config/config.example.yaml config/config.yaml
            log_success "配置文件已创建: config/config.yaml"
            log_warning "请编辑配置文件并填入必要的参数"
        else
            log_error "未找到配置模板文件"
            exit 1
        fi
    else
        log_warning "配置文件已存在，跳过创建"
    fi
}

# 创建必要目录
create_directories() {
    log_info "创建必要目录..."
    
    directories=("logs" "data" "temp" "screenshots" "downloads" "backup")
    
    for dir in "${directories[@]}"; do
        if [[ ! -d "$dir" ]]; then
            mkdir -p "$dir"
            log_info "创建目录: $dir"
        fi
    done
    
    log_success "目录创建完成"
}

# 设置权限
set_permissions() {
    log_info "设置文件权限..."
    
    chmod +x main.py
    chmod +x scripts/*.sh
    
    log_success "权限设置完成"
}

# 运行测试
run_tests() {
    log_info "运行基础测试..."
    
    if $PYTHON_CMD -c "import sys; print('Python版本:', sys.version)"; then
        log_success "Python环境测试通过"
    else
        log_error "Python环境测试失败"
        exit 1
    fi
    
    if $PYTHON_CMD -c "import selenium; print('Selenium版本:', selenium.__version__)"; then
        log_success "Selenium测试通过"
    else
        log_error "Selenium测试失败"
        exit 1
    fi
}

# 显示安装完成信息
show_completion_info() {
    log_success "安装完成！"
    echo
    echo "下一步操作："
    echo "1. 编辑配置文件: config/config.yaml"
    echo "2. 填入小米账号信息和其他必要配置"
    echo "3. 运行程序: python main.py"
    echo
    echo "更多信息请查看文档："
    echo "- 安装指南: docs/installation.md"
    echo "- 使用教程: docs/usage.md"
    echo "- 配置说明: docs/configuration.md"
    echo
}

# 主函数
main() {
    echo "========================================"
    echo "小米钱包视频会员自动化管理软件安装脚本"
    echo "========================================"
    echo
    
    # 检查是否在项目目录中
    if [[ ! -f "main.py" ]]; then
        log_error "请在项目根目录中运行此脚本"
        exit 1
    fi
    
    # 执行安装步骤
    check_os
    check_python
    install_system_deps
    install_chrome
    create_venv
    activate_venv
    install_python_deps
    setup_config
    create_directories
    set_permissions
    run_tests
    show_completion_info
}

# 错误处理
trap 'log_error "安装过程中发生错误，请检查上面的错误信息"' ERR

# 运行主函数
main "$@"
