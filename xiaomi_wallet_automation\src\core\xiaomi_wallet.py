"""
小米钱包视频会员管理器

负责处理小米钱包视频会员的自动领取和状态查询功能。
"""

import asyncio
import json
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException

from ..utils.logger import get_logger
from ..utils.browser_manager import BrowserManager
from ..models.account import XiaomiAccount
from ..models.task_result import TaskResult

logger = get_logger(__name__)


class XiaomiWalletManager:
    """小米钱包视频会员管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.browser_manager = BrowserManager(config.get('browser', {}))
        self.wallet_url = "https://wallet.mi.com"
        self.vip_url = "https://wallet.mi.com/vip"
        
    async def run_wallet_tasks(self, account: XiaomiAccount) -> TaskResult:
        """运行钱包相关任务"""
        logger.info(f"开始执行账号 {account.username} 的钱包任务")
        
        result = TaskResult(
            account_id=account.username,
            task_type="wallet_tasks",
            start_time=datetime.now()
        )
        
        try:
            # 启动浏览器
            driver = await self.browser_manager.get_driver()
            
            # 登录账号（假设已经在社区任务中登录）
            await self._navigate_to_wallet(driver)
            
            # 执行钱包任务
            tasks_config = self.config.get('tasks', {}).get('wallet', {})
            
            if tasks_config.get('auto_claim_vip', True):
                await self._claim_video_vip(driver, result)
            
            if tasks_config.get('check_vip_status', True):
                await self._check_vip_status(driver, result)
            
            result.success = True
            logger.info(f"账号 {account.username} 钱包任务执行完成")
            
        except Exception as e:
            logger.error(f"执行钱包任务时发生错误: {str(e)}")
            result.success = False
            result.error_message = str(e)
        
        finally:
            result.end_time = datetime.now()
            result.duration = (result.end_time - result.start_time).total_seconds()
            await self.browser_manager.close_driver()
        
        return result
    
    async def _navigate_to_wallet(self, driver: webdriver):
        """导航到小米钱包"""
        try:
            logger.info("导航到小米钱包")
            driver.get(self.wallet_url)
            await asyncio.sleep(3)
            
            # 检查是否需要登录
            if "login" in driver.current_url.lower():
                logger.warning("需要重新登录小米钱包")
                # 这里可以添加钱包特定的登录逻辑
                
        except Exception as e:
            logger.error(f"导航到小米钱包失败: {str(e)}")
            raise
    
    async def _claim_video_vip(self, driver: webdriver, result: TaskResult):
        """领取视频会员福利"""
        try:
            logger.info("开始领取视频会员福利")
            
            # 访问会员页面
            driver.get(self.vip_url)
            await asyncio.sleep(3)
            
            # 查找视频会员福利区域
            vip_sections = driver.find_elements(By.CSS_SELECTOR, ".vip-benefit-section")
            
            claimed_count = 0
            for section in vip_sections:
                try:
                    # 查找视频相关的福利
                    video_benefits = section.find_elements(By.CSS_SELECTOR, "[data-type*='video'], [class*='video']")
                    
                    for benefit in video_benefits:
                        # 查找领取按钮
                        claim_buttons = benefit.find_elements(By.CSS_SELECTOR, ".claim-btn, .receive-btn, .get-btn")
                        
                        for button in claim_buttons:
                            if "领取" in button.text and "已领取" not in button.text:
                                button.click()
                                await asyncio.sleep(2)
                                claimed_count += 1
                                logger.info("成功领取一个视频会员福利")
                                
                except Exception as e:
                    logger.warning(f"领取福利时发生错误: {str(e)}")
                    continue
            
            # 尝试其他可能的领取方式
            await self._try_alternative_claim_methods(driver, result)
            
            result.add_detail("claim_video_vip", f"视频会员福利领取完成，共领取 {claimed_count} 个福利")
            logger.info(f"视频会员福利领取完成，共领取 {claimed_count} 个福利")
            
        except Exception as e:
            logger.error(f"领取视频会员福利失败: {str(e)}")
            result.add_detail("claim_video_vip", f"领取失败: {str(e)}")
    
    async def _try_alternative_claim_methods(self, driver: webdriver, result: TaskResult):
        """尝试其他领取方式"""
        try:
            # 方法1: 查找通用的领取按钮
            claim_buttons = driver.find_elements(By.XPATH, "//button[contains(text(), '领取') or contains(text(), '免费领取')]")
            
            for button in claim_buttons:
                try:
                    if button.is_enabled() and button.is_displayed():
                        button.click()
                        await asyncio.sleep(2)
                        logger.info("通过通用按钮领取福利")
                except Exception:
                    continue
            
            # 方法2: 查找特定的视频会员区域
            video_sections = driver.find_elements(By.CSS_SELECTOR, "[data-service*='video'], [class*='video-vip']")
            
            for section in video_sections:
                try:
                    buttons = section.find_elements(By.TAG_NAME, "button")
                    for button in buttons:
                        if "领取" in button.text and button.is_enabled():
                            button.click()
                            await asyncio.sleep(2)
                            logger.info("通过视频专区领取福利")
                except Exception:
                    continue
            
            # 方法3: 执行JavaScript查找隐藏的领取功能
            try:
                driver.execute_script("""
                    var buttons = document.querySelectorAll('button, a, div[onclick]');
                    for (var i = 0; i < buttons.length; i++) {
                        var text = buttons[i].textContent || buttons[i].innerText;
                        if (text.includes('领取') || text.includes('免费') || text.includes('获取')) {
                            if (text.includes('视频') || text.includes('会员') || text.includes('VIP')) {
                                buttons[i].click();
                                break;
                            }
                        }
                    }
                """)
                await asyncio.sleep(2)
                logger.info("通过JavaScript尝试领取福利")
            except Exception:
                pass
                
        except Exception as e:
            logger.warning(f"尝试其他领取方式时发生错误: {str(e)}")
    
    async def _check_vip_status(self, driver: webdriver, result: TaskResult):
        """检查会员状态"""
        try:
            logger.info("检查视频会员状态")
            
            # 访问会员状态页面
            status_url = f"{self.vip_url}/status"
            driver.get(status_url)
            await asyncio.sleep(3)
            
            # 查找会员状态信息
            vip_info = await self._extract_vip_info(driver)
            
            if vip_info:
                result.add_detail("vip_status", vip_info)
                logger.info(f"会员状态: {vip_info}")
            else:
                # 尝试其他方式获取会员信息
                alternative_info = await self._get_alternative_vip_info(driver)
                result.add_detail("vip_status", alternative_info or "无法获取会员状态")
                
        except Exception as e:
            logger.error(f"检查会员状态失败: {str(e)}")
            result.add_detail("vip_status", f"状态检查失败: {str(e)}")
    
    async def _extract_vip_info(self, driver: webdriver) -> Optional[str]:
        """提取会员信息"""
        try:
            # 查找会员状态相关元素
            status_selectors = [
                ".vip-status",
                ".member-status", 
                ".subscription-info",
                "[class*='vip'][class*='status']",
                "[class*='member'][class*='info']"
            ]
            
            for selector in status_selectors:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    text = element.text.strip()
                    if text and ("会员" in text or "VIP" in text or "到期" in text):
                        return text
            
            # 查找到期时间
            expire_selectors = [
                "[class*='expire']",
                "[class*='valid']", 
                "[data-expire]",
                ".end-time",
                ".valid-time"
            ]
            
            for selector in expire_selectors:
                elements = driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    text = element.text.strip()
                    if text and ("2024" in text or "2025" in text or "天" in text):
                        return f"会员到期时间: {text}"
            
            return None
            
        except Exception as e:
            logger.warning(f"提取会员信息时发生错误: {str(e)}")
            return None
    
    async def _get_alternative_vip_info(self, driver: webdriver) -> Optional[str]:
        """获取替代的会员信息"""
        try:
            # 尝试通过JavaScript获取信息
            vip_info = driver.execute_script("""
                var info = '';
                var elements = document.querySelectorAll('*');
                for (var i = 0; i < elements.length; i++) {
                    var text = elements[i].textContent || elements[i].innerText;
                    if (text && (text.includes('会员') || text.includes('VIP'))) {
                        if (text.includes('到期') || text.includes('剩余') || text.includes('天')) {
                            info = text.trim();
                            break;
                        }
                    }
                }
                return info;
            """)
            
            if vip_info:
                return vip_info
            
            # 检查页面标题和URL
            page_title = driver.title
            if "会员" in page_title or "VIP" in page_title:
                return f"当前页面: {page_title}"
            
            # 查找任何包含数字和"天"的文本
            day_elements = driver.find_elements(By.XPATH, "//*[contains(text(), '天')]")
            for element in day_elements:
                text = element.text.strip()
                if any(char.isdigit() for char in text):
                    return f"可能的会员信息: {text}"
            
            return "未找到明确的会员状态信息"
            
        except Exception as e:
            logger.warning(f"获取替代会员信息时发生错误: {str(e)}")
            return None
    
    async def check_daily_benefits(self, account: XiaomiAccount) -> Dict:
        """检查每日福利"""
        logger.info(f"检查账号 {account.username} 的每日福利")
        
        benefits_info = {
            "account": account.username,
            "check_time": datetime.now().isoformat(),
            "benefits": []
        }
        
        try:
            driver = await self.browser_manager.get_driver()
            await self._navigate_to_wallet(driver)
            
            # 查找每日福利区域
            daily_sections = driver.find_elements(By.CSS_SELECTOR, "[class*='daily'], [class*='benefit']")
            
            for section in daily_sections:
                try:
                    benefit_text = section.text.strip()
                    if benefit_text:
                        benefits_info["benefits"].append(benefit_text)
                except Exception:
                    continue
            
        except Exception as e:
            logger.error(f"检查每日福利失败: {str(e)}")
            benefits_info["error"] = str(e)
        
        finally:
            await self.browser_manager.close_driver()
        
        return benefits_info
