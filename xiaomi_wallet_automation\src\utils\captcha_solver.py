"""
验证码识别工具

支持多种验证码识别服务，包括2captcha等。
"""

import asyncio
import base64
import io
import time
from typing import Dict, Optional, Union
from pathlib import Path

import requests
from PIL import Image
from twocaptcha import TwoCaptcha

from .logger import get_logger

logger = get_logger(__name__)


class CaptchaSolver:
    """验证码识别器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.service = config.get('service', '2captcha')
        self.api_key = config.get('api_key')
        self.timeout = config.get('timeout', 120)
        self.retry_times = config.get('retry_times', 3)
        
        # 初始化2captcha客户端
        if self.service == '2captcha' and self.api_key:
            self.solver = TwoCaptcha(self.api_key)
        else:
            self.solver = None
            
    async def solve_captcha(self, captcha_input: Union[str, bytes, Image.Image]) -> Optional[str]:
        """
        识别验证码
        
        Args:
            captcha_input: 验证码输入，可以是URL、图片字节、PIL图片对象
            
        Returns:
            识别结果文本，失败返回None
        """
        logger.info(f"开始识别验证码，使用服务: {self.service}")
        
        try:
            # 处理不同类型的输入
            image_data = await self._process_captcha_input(captcha_input)
            
            if not image_data:
                logger.error("无法获取验证码图片数据")
                return None
            
            # 根据服务类型进行识别
            if self.service == '2captcha':
                return await self._solve_with_2captcha(image_data)
            elif self.service == 'manual':
                return await self._solve_manually(image_data)
            else:
                logger.error(f"不支持的验证码识别服务: {self.service}")
                return None
                
        except Exception as e:
            logger.error(f"验证码识别失败: {str(e)}")
            return None
    
    async def _process_captcha_input(self, captcha_input: Union[str, bytes, Image.Image]) -> Optional[bytes]:
        """处理验证码输入"""
        try:
            if isinstance(captcha_input, str):
                # URL或文件路径
                if captcha_input.startswith(('http://', 'https://')):
                    # 从URL下载图片
                    response = requests.get(captcha_input, timeout=10)
                    response.raise_for_status()
                    return response.content
                else:
                    # 本地文件路径
                    with open(captcha_input, 'rb') as f:
                        return f.read()
                        
            elif isinstance(captcha_input, bytes):
                # 直接的字节数据
                return captcha_input
                
            elif isinstance(captcha_input, Image.Image):
                # PIL图片对象
                buffer = io.BytesIO()
                captcha_input.save(buffer, format='PNG')
                return buffer.getvalue()
                
            else:
                logger.error(f"不支持的验证码输入类型: {type(captcha_input)}")
                return None
                
        except Exception as e:
            logger.error(f"处理验证码输入失败: {str(e)}")
            return None
    
    async def _solve_with_2captcha(self, image_data: bytes) -> Optional[str]:
        """使用2captcha服务识别验证码"""
        if not self.solver:
            logger.error("2captcha客户端未初始化")
            return None
        
        try:
            # 将图片数据转换为base64
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 提交验证码识别任务
            logger.info("提交验证码到2captcha服务")
            result = self.solver.normal(image_base64)
            
            if result and 'code' in result:
                captcha_text = result['code']
                logger.info(f"2captcha识别成功: {captcha_text}")
                return captcha_text
            else:
                logger.error("2captcha识别失败")
                return None
                
        except Exception as e:
            logger.error(f"2captcha识别过程中发生错误: {str(e)}")
            return None
    
    async def _solve_manually(self, image_data: bytes) -> Optional[str]:
        """手动识别验证码"""
        try:
            # 保存验证码图片到临时文件
            temp_dir = Path('temp')
            temp_dir.mkdir(exist_ok=True)
            
            timestamp = int(time.time())
            temp_file = temp_dir / f"captcha_{timestamp}.png"
            
            with open(temp_file, 'wb') as f:
                f.write(image_data)
            
            logger.info(f"验证码图片已保存到: {temp_file}")
            logger.info("请查看验证码图片并手动输入识别结果")
            
            # 等待用户输入
            captcha_text = input("请输入验证码: ").strip()
            
            # 清理临时文件
            try:
                temp_file.unlink()
            except:
                pass
            
            if captcha_text:
                logger.info(f"手动识别结果: {captcha_text}")
                return captcha_text
            else:
                logger.warning("未输入验证码")
                return None
                
        except Exception as e:
            logger.error(f"手动识别验证码失败: {str(e)}")
            return None
    
    async def solve_recaptcha(self, site_key: str, page_url: str) -> Optional[str]:
        """识别reCAPTCHA"""
        if not self.solver:
            logger.error("验证码识别服务未初始化")
            return None
        
        try:
            logger.info("开始识别reCAPTCHA")
            
            result = self.solver.recaptcha(
                sitekey=site_key,
                url=page_url
            )
            
            if result and 'code' in result:
                logger.info("reCAPTCHA识别成功")
                return result['code']
            else:
                logger.error("reCAPTCHA识别失败")
                return None
                
        except Exception as e:
            logger.error(f"reCAPTCHA识别失败: {str(e)}")
            return None
    
    async def solve_hcaptcha(self, site_key: str, page_url: str) -> Optional[str]:
        """识别hCaptcha"""
        if not self.solver:
            logger.error("验证码识别服务未初始化")
            return None
        
        try:
            logger.info("开始识别hCaptcha")
            
            result = self.solver.hcaptcha(
                sitekey=site_key,
                url=page_url
            )
            
            if result and 'code' in result:
                logger.info("hCaptcha识别成功")
                return result['code']
            else:
                logger.error("hCaptcha识别失败")
                return None
                
        except Exception as e:
            logger.error(f"hCaptcha识别失败: {str(e)}")
            return None
    
    def get_balance(self) -> Optional[float]:
        """获取账户余额"""
        if not self.solver:
            return None
        
        try:
            balance = self.solver.balance()
            logger.info(f"2captcha账户余额: ${balance}")
            return float(balance)
        except Exception as e:
            logger.error(f"获取账户余额失败: {str(e)}")
            return None
    
    def report_good(self, captcha_id: str) -> bool:
        """报告验证码识别正确"""
        if not self.solver:
            return False
        
        try:
            self.solver.report(captcha_id, True)
            logger.info(f"已报告验证码 {captcha_id} 识别正确")
            return True
        except Exception as e:
            logger.error(f"报告验证码正确失败: {str(e)}")
            return False
    
    def report_bad(self, captcha_id: str) -> bool:
        """报告验证码识别错误"""
        if not self.solver:
            return False
        
        try:
            self.solver.report(captcha_id, False)
            logger.info(f"已报告验证码 {captcha_id} 识别错误")
            return True
        except Exception as e:
            logger.error(f"报告验证码错误失败: {str(e)}")
            return False
    
    async def preprocess_image(self, image_data: bytes) -> bytes:
        """预处理验证码图片"""
        try:
            # 打开图片
            image = Image.open(io.BytesIO(image_data))
            
            # 转换为RGB模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 调整大小（如果太小）
            width, height = image.size
            if width < 100 or height < 40:
                new_width = max(100, width * 2)
                new_height = max(40, height * 2)
                image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)
            
            # 增强对比度
            from PIL import ImageEnhance
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.5)
            
            # 保存处理后的图片
            buffer = io.BytesIO()
            image.save(buffer, format='PNG')
            
            logger.info("验证码图片预处理完成")
            return buffer.getvalue()
            
        except Exception as e:
            logger.error(f"验证码图片预处理失败: {str(e)}")
            return image_data  # 返回原始数据
    
    async def solve_with_retry(self, captcha_input: Union[str, bytes, Image.Image]) -> Optional[str]:
        """带重试的验证码识别"""
        for attempt in range(self.retry_times):
            try:
                logger.info(f"验证码识别尝试 {attempt + 1}/{self.retry_times}")
                
                result = await self.solve_captcha(captcha_input)
                
                if result:
                    return result
                
                if attempt < self.retry_times - 1:
                    await asyncio.sleep(2)  # 重试前等待
                    
            except Exception as e:
                logger.error(f"验证码识别尝试 {attempt + 1} 失败: {str(e)}")
                
                if attempt < self.retry_times - 1:
                    await asyncio.sleep(2)
        
        logger.error("所有验证码识别尝试均失败")
        return None


# 全局验证码识别器实例
_captcha_solver: Optional[CaptchaSolver] = None


def get_captcha_solver(config: Dict = None) -> CaptchaSolver:
    """获取全局验证码识别器实例"""
    global _captcha_solver
    
    if _captcha_solver is None and config:
        _captcha_solver = CaptchaSolver(config)
    
    return _captcha_solver
