"""
小米账号数据模型

定义小米账号相关的数据结构和状态管理。
"""

import json
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

from ..utils.encryption_utils import EncryptionUtils


class AccountStatus(Enum):
    """账号状态枚举"""
    ACTIVE = "active"           # 活跃
    INACTIVE = "inactive"       # 非活跃
    SUSPENDED = "suspended"     # 暂停
    BANNED = "banned"          # 封禁
    ERROR = "error"            # 错误状态


@dataclass
class XiaomiAccount:
    """小米账号数据模型"""
    username: str
    password: str
    phone: str
    enabled: bool = True
    status: AccountStatus = AccountStatus.ACTIVE
    
    # 登录相关
    session_data: Optional[Dict] = None
    last_login_time: Optional[datetime] = None
    login_count: int = 0
    
    # 任务相关
    last_task_time: Optional[datetime] = None
    total_tasks_completed: int = 0
    consecutive_success_days: int = 0
    
    # 会员信息
    vip_status: Optional[str] = None
    vip_expire_time: Optional[datetime] = None
    growth_value: int = 0
    
    # 错误信息
    last_error: Optional[str] = None
    error_count: int = 0
    
    # 创建和更新时间
    created_time: datetime = None
    updated_time: datetime = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.created_time is None:
            self.created_time = datetime.now()
        self.updated_time = datetime.now()
        
        # 确保状态是枚举类型
        if isinstance(self.status, str):
            self.status = AccountStatus(self.status)
    
    def update_login_info(self, session_data: Dict = None):
        """更新登录信息"""
        self.last_login_time = datetime.now()
        self.login_count += 1
        if session_data:
            self.session_data = session_data
        self.updated_time = datetime.now()
    
    def update_task_info(self, success: bool = True):
        """更新任务信息"""
        self.last_task_time = datetime.now()
        if success:
            self.total_tasks_completed += 1
            self.consecutive_success_days += 1
            self.error_count = 0  # 重置错误计数
        else:
            self.consecutive_success_days = 0
            self.error_count += 1
        self.updated_time = datetime.now()
    
    def update_vip_info(self, vip_status: str, vip_expire_time: datetime = None):
        """更新会员信息"""
        self.vip_status = vip_status
        self.vip_expire_time = vip_expire_time
        self.updated_time = datetime.now()
    
    def update_growth_value(self, growth_value: int):
        """更新成长值"""
        self.growth_value = growth_value
        self.updated_time = datetime.now()
    
    def set_error(self, error_message: str):
        """设置错误信息"""
        self.last_error = error_message
        self.error_count += 1
        self.status = AccountStatus.ERROR
        self.updated_time = datetime.now()
    
    def clear_error(self):
        """清除错误信息"""
        self.last_error = None
        self.error_count = 0
        self.status = AccountStatus.ACTIVE
        self.updated_time = datetime.now()
    
    def is_session_valid(self, max_age_hours: int = 24) -> bool:
        """检查会话是否有效"""
        if not self.session_data or not self.last_login_time:
            return False
        
        age = datetime.now() - self.last_login_time
        return age.total_seconds() < max_age_hours * 3600
    
    def should_run_tasks(self) -> bool:
        """判断是否应该执行任务"""
        if not self.enabled or self.status != AccountStatus.ACTIVE:
            return False
        
        # 检查错误次数
        if self.error_count >= 5:
            return False
        
        # 检查最后任务时间（避免重复执行）
        if self.last_task_time:
            time_diff = datetime.now() - self.last_task_time
            if time_diff.total_seconds() < 3600:  # 1小时内不重复执行
                return False
        
        return True
    
    def get_summary(self) -> Dict:
        """获取账号摘要信息"""
        return {
            "username": self.username,
            "phone": self.phone,
            "status": self.status.value,
            "enabled": self.enabled,
            "login_count": self.login_count,
            "total_tasks_completed": self.total_tasks_completed,
            "consecutive_success_days": self.consecutive_success_days,
            "growth_value": self.growth_value,
            "vip_status": self.vip_status,
            "last_login_time": self.last_login_time.isoformat() if self.last_login_time else None,
            "last_task_time": self.last_task_time.isoformat() if self.last_task_time else None,
            "error_count": self.error_count,
            "last_error": self.last_error
        }
    
    def to_dict(self, include_sensitive: bool = False) -> Dict:
        """转换为字典"""
        data = asdict(self)
        
        # 处理枚举类型
        data['status'] = self.status.value
        
        # 处理日期时间
        for field in ['created_time', 'updated_time', 'last_login_time', 'last_task_time', 'vip_expire_time']:
            if data[field]:
                data[field] = data[field].isoformat()
        
        # 是否包含敏感信息
        if not include_sensitive:
            data.pop('password', None)
            data.pop('session_data', None)
        
        return data
    
    @classmethod
    def from_dict(cls, data: Dict) -> 'XiaomiAccount':
        """从字典创建账号对象"""
        # 处理日期时间字段
        datetime_fields = ['created_time', 'updated_time', 'last_login_time', 'last_task_time', 'vip_expire_time']
        for field in datetime_fields:
            if data.get(field):
                data[field] = datetime.fromisoformat(data[field])
        
        # 处理状态枚举
        if 'status' in data and isinstance(data['status'], str):
            data['status'] = AccountStatus(data['status'])
        
        return cls(**data)


class AccountManager:
    """账号管理器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.accounts: List[XiaomiAccount] = []
        self.data_file = Path('data/accounts.json')
        self.encryption_utils = EncryptionUtils(config.get('security', {}))
        
        # 确保数据目录存在
        self.data_file.parent.mkdir(exist_ok=True)
        
        # 加载账号数据
        self.load_accounts()
    
    def load_accounts(self):
        """加载账号数据"""
        try:
            if self.data_file.exists():
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                self.accounts = []
                for account_data in data.get('accounts', []):
                    # 解密密码
                    if account_data.get('password'):
                        account_data['password'] = self.encryption_utils.decrypt(account_data['password'])
                    
                    account = XiaomiAccount.from_dict(account_data)
                    self.accounts.append(account)
                
                print(f"已加载 {len(self.accounts)} 个账号")
            else:
                # 从配置文件加载初始账号
                self.load_from_config()
                
        except Exception as e:
            print(f"加载账号数据失败: {str(e)}")
            self.load_from_config()
    
    def load_from_config(self):
        """从配置文件加载账号"""
        try:
            accounts_config = self.config.get('xiaomi', {}).get('accounts', [])
            
            self.accounts = []
            for account_config in accounts_config:
                account = XiaomiAccount(
                    username=account_config['username'],
                    password=account_config['password'],
                    phone=account_config['phone'],
                    enabled=account_config.get('enabled', True)
                )
                self.accounts.append(account)
            
            print(f"从配置文件加载 {len(self.accounts)} 个账号")
            
            # 保存到数据文件
            self.save_accounts()
            
        except Exception as e:
            print(f"从配置文件加载账号失败: {str(e)}")
    
    def save_accounts(self):
        """保存账号数据"""
        try:
            accounts_data = []
            for account in self.accounts:
                account_dict = account.to_dict(include_sensitive=True)
                
                # 加密密码
                if account_dict.get('password'):
                    account_dict['password'] = self.encryption_utils.encrypt(account_dict['password'])
                
                accounts_data.append(account_dict)
            
            data = {
                'accounts': accounts_data,
                'updated_time': datetime.now().isoformat()
            }
            
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
                
        except Exception as e:
            print(f"保存账号数据失败: {str(e)}")
    
    def get_enabled_accounts(self) -> List[XiaomiAccount]:
        """获取启用的账号"""
        return [account for account in self.accounts if account.enabled and account.should_run_tasks()]
    
    def get_account_by_username(self, username: str) -> Optional[XiaomiAccount]:
        """根据用户名获取账号"""
        for account in self.accounts:
            if account.username == username:
                return account
        return None
    
    def update_account(self, account: XiaomiAccount):
        """更新账号信息"""
        for i, existing_account in enumerate(self.accounts):
            if existing_account.username == account.username:
                self.accounts[i] = account
                break
        
        # 保存更新
        self.save_accounts()
    
    def get_accounts_summary(self) -> List[Dict]:
        """获取所有账号的摘要信息"""
        return [account.get_summary() for account in self.accounts]
