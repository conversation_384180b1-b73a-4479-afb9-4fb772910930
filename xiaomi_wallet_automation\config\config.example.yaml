# 小米钱包视频会员自动化管理软件配置文件

# 基础配置
app:
  name: "小米钱包自动化管理"
  version: "1.0.0"
  debug: false
  log_level: "INFO"

# 小米账号配置
xiaomi:
  accounts:
    - username: "your_username_1"
      password: "your_password_1"
      phone: "your_phone_1"
      enabled: true
    - username: "your_username_2"
      password: "your_password_2"
      phone: "your_phone_2"
      enabled: false
  
  # 登录配置
  login:
    timeout: 30
    retry_times: 3
    use_qr_login: true
    save_session: true

# 任务配置
tasks:
  # 小米社区任务
  community:
    enabled: true
    daily_checkin: true
    browse_tasks:
      user_profile: true
      community_posts: true
      video_posts: true
      browse_count: 5
    interaction_tasks:
      like_posts: true
      follow_sections: true
      max_likes_per_day: 10
    special_tasks:
      special_pages: true
      wechat_miniprogram: true
      carrot_tasks: true
  
  # 小米钱包视频会员任务
  wallet:
    enabled: true
    auto_claim_vip: true
    check_vip_status: true
    claim_retry_times: 3

# 验证码配置
captcha:
  service: "2captcha"  # 支持: 2captcha, manual
  api_key: "your_2captcha_api_key"
  timeout: 120
  retry_times: 3

# 推送通知配置
notifications:
  enabled: true
  services:
    # 微信推送
    wechat:
      enabled: false
      corpid: "your_corpid"
      corpsecret: "your_corpsecret"
      agentid: "your_agentid"
      touser: "@all"
    
    # 钉钉推送
    dingtalk:
      enabled: false
      webhook: "your_dingtalk_webhook"
      secret: "your_dingtalk_secret"
    
    # 企业微信推送
    work_wechat:
      enabled: false
      webhook: "your_work_wechat_webhook"
    
    # 邮件推送
    email:
      enabled: false
      smtp_server: "smtp.gmail.com"
      smtp_port: 587
      username: "<EMAIL>"
      password: "your_email_password"
      to_emails: ["<EMAIL>"]

# 调度配置
scheduler:
  enabled: true
  timezone: "Asia/Shanghai"
  jobs:
    - name: "daily_tasks"
      cron: "0 9 * * *"  # 每天上午9点执行
      function: "run_daily_tasks"
    - name: "vip_check"
      cron: "0 12,18 * * *"  # 每天12点和18点检查会员状态
      function: "check_vip_status"

# 浏览器配置
browser:
  type: "chrome"  # 支持: chrome, firefox, edge
  headless: true
  window_size: "1920,1080"
  user_agent: "auto"  # auto 或自定义 user agent
  proxy: null  # 代理配置，格式: "http://proxy:port"
  
  # Chrome 特定配置
  chrome_options:
    - "--no-sandbox"
    - "--disable-dev-shm-usage"
    - "--disable-gpu"
    - "--disable-extensions"

# 数据存储配置
storage:
  type: "sqlite"  # 支持: sqlite, mysql, postgresql
  database_url: "sqlite:///data/xiaomi_automation.db"
  backup_enabled: true
  backup_interval: 24  # 小时

# 日志配置
logging:
  level: "INFO"
  format: "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}"
  rotation: "1 day"
  retention: "30 days"
  compression: "zip"
  
  # 日志文件配置
  files:
    main: "logs/main.log"
    error: "logs/error.log"
    task: "logs/task.log"
    notification: "logs/notification.log"

# 安全配置
security:
  encrypt_passwords: true
  encryption_key: "your_encryption_key"  # 32字符的加密密钥
  session_timeout: 3600  # 会话超时时间（秒）

# 性能配置
performance:
  max_concurrent_accounts: 3
  request_delay: 1  # 请求间隔（秒）
  retry_delay: 5  # 重试间隔（秒）
  timeout: 30  # 请求超时时间（秒）

# 开发配置
development:
  mock_mode: false  # 模拟模式，用于测试
  save_screenshots: true  # 保存截图
  save_html: false  # 保存页面HTML
