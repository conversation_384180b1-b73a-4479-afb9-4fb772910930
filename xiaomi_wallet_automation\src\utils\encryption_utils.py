"""
加密工具

提供密码加密、解密和安全存储功能。
"""

import os
import base64
import hashlib
from typing import Optional, Union
from cryptography.fernet import Ferne<PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC

from .logger import get_logger

logger = get_logger(__name__)


class EncryptionUtils:
    """加密工具类"""
    
    def __init__(self, config: dict):
        self.config = config
        self.encryption_enabled = config.get('encrypt_passwords', True)
        self.encryption_key = config.get('encryption_key')
        self._fernet = None
        
        if self.encryption_enabled:
            self._initialize_encryption()
    
    def _initialize_encryption(self):
        """初始化加密"""
        try:
            if not self.encryption_key:
                # 生成新的加密密钥
                self.encryption_key = self._generate_key()
                logger.warning("未配置加密密钥，已生成新密钥。请将其保存到配置文件中。")
                logger.info(f"生成的加密密钥: {self.encryption_key}")
            
            # 创建Fernet实例
            key = self._derive_key(self.encryption_key)
            self._fernet = Fernet(key)
            
            logger.info("加密系统初始化成功")
            
        except Exception as e:
            logger.error(f"初始化加密系统失败: {str(e)}")
            self.encryption_enabled = False
    
    def _generate_key(self) -> str:
        """生成加密密钥"""
        return base64.urlsafe_b64encode(os.urandom(32)).decode()
    
    def _derive_key(self, password: str) -> bytes:
        """从密码派生密钥"""
        # 使用固定的盐值（在生产环境中应该使用随机盐值）
        salt = b'xiaomi_wallet_automation_salt'
        
        kdf = PBKDF2HMAC(
            algorithm=hashes.SHA256(),
            length=32,
            salt=salt,
            iterations=100000,
        )
        
        key = base64.urlsafe_b64encode(kdf.derive(password.encode()))
        return key
    
    def encrypt(self, plaintext: str) -> str:
        """加密文本"""
        if not self.encryption_enabled or not self._fernet:
            return plaintext
        
        try:
            encrypted_data = self._fernet.encrypt(plaintext.encode())
            return base64.urlsafe_b64encode(encrypted_data).decode()
        except Exception as e:
            logger.error(f"加密失败: {str(e)}")
            return plaintext
    
    def decrypt(self, encrypted_text: str) -> str:
        """解密文本"""
        if not self.encryption_enabled or not self._fernet:
            return encrypted_text
        
        try:
            encrypted_data = base64.urlsafe_b64decode(encrypted_text.encode())
            decrypted_data = self._fernet.decrypt(encrypted_data)
            return decrypted_data.decode()
        except Exception as e:
            logger.error(f"解密失败: {str(e)}")
            return encrypted_text
    
    def encrypt_dict(self, data: dict, keys_to_encrypt: list) -> dict:
        """加密字典中的指定键值"""
        encrypted_data = data.copy()
        
        for key in keys_to_encrypt:
            if key in encrypted_data:
                encrypted_data[key] = self.encrypt(str(encrypted_data[key]))
        
        return encrypted_data
    
    def decrypt_dict(self, data: dict, keys_to_decrypt: list) -> dict:
        """解密字典中的指定键值"""
        decrypted_data = data.copy()
        
        for key in keys_to_decrypt:
            if key in decrypted_data:
                decrypted_data[key] = self.decrypt(str(decrypted_data[key]))
        
        return decrypted_data
    
    def hash_password(self, password: str) -> str:
        """对密码进行哈希"""
        return hashlib.sha256(password.encode()).hexdigest()
    
    def verify_password(self, password: str, hashed_password: str) -> bool:
        """验证密码"""
        return self.hash_password(password) == hashed_password
    
    def generate_session_token(self, user_id: str) -> str:
        """生成会话令牌"""
        import time
        import uuid
        
        data = f"{user_id}:{time.time()}:{uuid.uuid4()}"
        return self.hash_password(data)
    
    def is_encrypted(self, text: str) -> bool:
        """判断文本是否已加密"""
        try:
            # 尝试解码base64
            base64.urlsafe_b64decode(text.encode())
            return True
        except:
            return False


class SecureStorage:
    """安全存储类"""
    
    def __init__(self, encryption_utils: EncryptionUtils):
        self.encryption_utils = encryption_utils
        self.storage_file = 'data/secure_storage.json'
        
        # 确保存储目录存在
        os.makedirs(os.path.dirname(self.storage_file), exist_ok=True)
    
    def store(self, key: str, value: str) -> bool:
        """安全存储数据"""
        try:
            import json
            
            # 读取现有数据
            data = {}
            if os.path.exists(self.storage_file):
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
            
            # 加密并存储
            encrypted_value = self.encryption_utils.encrypt(value)
            data[key] = encrypted_value
            
            # 保存数据
            with open(self.storage_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            
            logger.info(f"数据已安全存储: {key}")
            return True
            
        except Exception as e:
            logger.error(f"安全存储数据失败: {str(e)}")
            return False
    
    def retrieve(self, key: str) -> Optional[str]:
        """安全检索数据"""
        try:
            import json
            
            if not os.path.exists(self.storage_file):
                return None
            
            with open(self.storage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if key not in data:
                return None
            
            # 解密并返回
            encrypted_value = data[key]
            decrypted_value = self.encryption_utils.decrypt(encrypted_value)
            
            logger.info(f"数据已安全检索: {key}")
            return decrypted_value
            
        except Exception as e:
            logger.error(f"安全检索数据失败: {str(e)}")
            return None
    
    def delete(self, key: str) -> bool:
        """删除存储的数据"""
        try:
            import json
            
            if not os.path.exists(self.storage_file):
                return True
            
            with open(self.storage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            if key in data:
                del data[key]
                
                with open(self.storage_file, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                
                logger.info(f"数据已删除: {key}")
            
            return True
            
        except Exception as e:
            logger.error(f"删除数据失败: {str(e)}")
            return False
    
    def list_keys(self) -> list:
        """列出所有存储的键"""
        try:
            import json
            
            if not os.path.exists(self.storage_file):
                return []
            
            with open(self.storage_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            return list(data.keys())
            
        except Exception as e:
            logger.error(f"列出存储键失败: {str(e)}")
            return []


class PasswordManager:
    """密码管理器"""
    
    def __init__(self, encryption_utils: EncryptionUtils):
        self.encryption_utils = encryption_utils
        self.secure_storage = SecureStorage(encryption_utils)
    
    def store_password(self, account_id: str, password: str) -> bool:
        """存储密码"""
        key = f"password_{account_id}"
        return self.secure_storage.store(key, password)
    
    def get_password(self, account_id: str) -> Optional[str]:
        """获取密码"""
        key = f"password_{account_id}"
        return self.secure_storage.retrieve(key)
    
    def update_password(self, account_id: str, new_password: str) -> bool:
        """更新密码"""
        return self.store_password(account_id, new_password)
    
    def delete_password(self, account_id: str) -> bool:
        """删除密码"""
        key = f"password_{account_id}"
        return self.secure_storage.delete(key)
    
    def store_session_data(self, account_id: str, session_data: dict) -> bool:
        """存储会话数据"""
        import json
        key = f"session_{account_id}"
        session_json = json.dumps(session_data, ensure_ascii=False)
        return self.secure_storage.store(key, session_json)
    
    def get_session_data(self, account_id: str) -> Optional[dict]:
        """获取会话数据"""
        import json
        key = f"session_{account_id}"
        session_json = self.secure_storage.retrieve(key)
        
        if session_json:
            try:
                return json.loads(session_json)
            except json.JSONDecodeError:
                logger.error(f"会话数据格式错误: {account_id}")
                return None
        
        return None
    
    def clear_session_data(self, account_id: str) -> bool:
        """清除会话数据"""
        key = f"session_{account_id}"
        return self.secure_storage.delete(key)
    
    def get_all_accounts(self) -> list:
        """获取所有存储密码的账号"""
        keys = self.secure_storage.list_keys()
        accounts = []
        
        for key in keys:
            if key.startswith('password_'):
                account_id = key[9:]  # 移除 'password_' 前缀
                accounts.append(account_id)
        
        return accounts


def generate_secure_key() -> str:
    """生成安全密钥"""
    return base64.urlsafe_b64encode(os.urandom(32)).decode()


def create_encryption_utils(config: dict) -> EncryptionUtils:
    """创建加密工具实例"""
    return EncryptionUtils(config)


# 全局加密工具实例
_encryption_utils: Optional[EncryptionUtils] = None


def get_encryption_utils(config: dict = None) -> EncryptionUtils:
    """获取全局加密工具实例"""
    global _encryption_utils
    
    if _encryption_utils is None and config:
        _encryption_utils = EncryptionUtils(config)
    
    return _encryption_utils
