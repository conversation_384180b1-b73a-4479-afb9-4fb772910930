"""
任务调度器

负责定时任务的调度和执行。
"""

import asyncio
import schedule
from datetime import datetime, time
from typing import Dict, List, Callable
from concurrent.futures import ThreadPoolExecutor

from ..utils.logger import get_logger
from .automation_engine import AutomationEngine

logger = get_logger(__name__)


class TaskScheduler:
    """任务调度器"""
    
    def __init__(self, config: Dict, automation_engine: AutomationEngine):
        self.config = config
        self.automation_engine = automation_engine
        self.scheduler_config = config.get('scheduler', {})
        self.timezone = self.scheduler_config.get('timezone', 'Asia/Shanghai')
        self.jobs = self.scheduler_config.get('jobs', [])
        
        self.running = False
        self.executor = ThreadPoolExecutor(max_workers=1)
        
        # 设置时区
        import os
        os.environ['TZ'] = self.timezone
        
        # 注册默认任务
        self._register_default_jobs()
    
    def _register_default_jobs(self):
        """注册默认任务"""
        # 如果配置中没有任务，添加默认任务
        if not self.jobs:
            self.jobs = [
                {
                    'name': 'daily_tasks',
                    'cron': '0 9 * * *',  # 每天上午9点
                    'function': 'run_daily_tasks'
                },
                {
                    'name': 'vip_check',
                    'cron': '0 12,18 * * *',  # 每天12点和18点
                    'function': 'check_vip_status'
                }
            ]
    
    async def start(self):
        """启动调度器"""
        logger.info("启动任务调度器")
        
        self.running = True
        
        # 注册所有任务
        for job_config in self.jobs:
            self._register_job(job_config)
        
        logger.info(f"已注册 {len(self.jobs)} 个定时任务")
        
        # 启动调度循环
        await self._run_scheduler_loop()
    
    async def stop(self):
        """停止调度器"""
        logger.info("停止任务调度器")
        
        self.running = False
        
        # 清除所有任务
        schedule.clear()
        
        # 关闭线程池
        self.executor.shutdown(wait=True)
        
        logger.info("任务调度器已停止")
    
    def _register_job(self, job_config: Dict):
        """注册单个任务"""
        job_name = job_config.get('name', 'unknown')
        cron_expr = job_config.get('cron', '0 9 * * *')
        function_name = job_config.get('function', 'run_daily_tasks')
        
        try:
            # 解析cron表达式
            cron_parts = cron_expr.split()
            if len(cron_parts) != 5:
                logger.error(f"无效的cron表达式: {cron_expr}")
                return
            
            minute, hour, day, month, weekday = cron_parts
            
            # 获取对应的函数
            func = self._get_job_function(function_name)
            if not func:
                logger.error(f"未找到函数: {function_name}")
                return
            
            # 注册任务
            if weekday != '*':
                # 按星期调度
                weekday_map = {
                    '0': schedule.every().sunday,
                    '1': schedule.every().monday,
                    '2': schedule.every().tuesday,
                    '3': schedule.every().wednesday,
                    '4': schedule.every().thursday,
                    '5': schedule.every().friday,
                    '6': schedule.every().saturday,
                    '7': schedule.every().sunday
                }
                
                if weekday in weekday_map:
                    job = weekday_map[weekday].at(f"{hour}:{minute}")
                    job.do(self._run_async_job, func, job_name)
            else:
                # 每天调度
                job = schedule.every().day.at(f"{hour}:{minute}")
                job.do(self._run_async_job, func, job_name)
            
            logger.info(f"已注册任务: {job_name} ({cron_expr})")
            
        except Exception as e:
            logger.error(f"注册任务失败 {job_name}: {str(e)}")
    
    def _get_job_function(self, function_name: str) -> Callable:
        """获取任务函数"""
        function_map = {
            'run_daily_tasks': self.run_daily_tasks,
            'check_vip_status': self.check_vip_status,
            'send_daily_summary': self.send_daily_summary,
            'test_notifications': self.test_notifications
        }
        
        return function_map.get(function_name)
    
    def _run_async_job(self, func: Callable, job_name: str):
        """运行异步任务"""
        try:
            logger.info(f"开始执行定时任务: {job_name}")
            
            # 在新的事件循环中运行异步函数
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                loop.run_until_complete(func())
                logger.info(f"定时任务执行完成: {job_name}")
            finally:
                loop.close()
                
        except Exception as e:
            logger.error(f"定时任务执行失败 {job_name}: {str(e)}")
    
    async def _run_scheduler_loop(self):
        """运行调度循环"""
        logger.info("调度器循环开始")
        
        while self.running:
            try:
                # 检查并运行待执行的任务
                schedule.run_pending()
                
                # 等待1秒
                await asyncio.sleep(1)
                
            except Exception as e:
                logger.error(f"调度器循环发生错误: {str(e)}")
                await asyncio.sleep(5)  # 错误时等待更长时间
        
        logger.info("调度器循环结束")
    
    async def run_daily_tasks(self):
        """运行每日任务"""
        logger.info("执行每日任务")
        
        try:
            # 获取启用的账号
            enabled_accounts = self.automation_engine.account_manager.get_enabled_accounts()
            
            if not enabled_accounts:
                logger.warning("没有找到启用的账号")
                return
            
            # 执行所有账号的任务
            results = await self.automation_engine.run_all_accounts_tasks(enabled_accounts)
            
            # 生成汇总报告
            summary = self.automation_engine.generate_summary_report(results)
            
            # 发送汇总通知
            await self.automation_engine.send_summary_notification(summary)
            
            logger.info(f"每日任务执行完成，处理 {len(enabled_accounts)} 个账号")
            
        except Exception as e:
            logger.error(f"每日任务执行失败: {str(e)}")
            
            # 发送错误通知
            await self.automation_engine.notification_manager.send_error_notification(
                "系统", f"每日任务执行失败: {str(e)}"
            )
    
    async def check_vip_status(self):
        """检查会员状态"""
        logger.info("执行会员状态检查")
        
        try:
            # 获取启用的账号
            enabled_accounts = self.automation_engine.account_manager.get_enabled_accounts()
            
            if not enabled_accounts:
                logger.warning("没有找到启用的账号")
                return
            
            # 检查会员状态
            vip_results = await self.automation_engine.run_vip_status_check(enabled_accounts)
            
            logger.info(f"会员状态检查完成，检查 {len(vip_results)} 个账号")
            
        except Exception as e:
            logger.error(f"会员状态检查失败: {str(e)}")
    
    async def send_daily_summary(self):
        """发送每日汇总"""
        logger.info("发送每日汇总")
        
        try:
            # 获取今日的任务结果
            daily_summary = self.automation_engine.task_result_manager.get_daily_summary()
            
            # 发送汇总通知
            await self.automation_engine.notification_manager.send_daily_summary_notification(daily_summary)
            
            logger.info("每日汇总发送完成")
            
        except Exception as e:
            logger.error(f"发送每日汇总失败: {str(e)}")
    
    async def test_notifications(self):
        """测试通知服务"""
        logger.info("测试通知服务")
        
        try:
            # 测试所有通知服务
            test_results = await self.automation_engine.test_notification_services()
            
            logger.info(f"通知服务测试完成: {test_results}")
            
        except Exception as e:
            logger.error(f"测试通知服务失败: {str(e)}")
    
    def add_job(self, job_config: Dict):
        """添加新任务"""
        try:
            self.jobs.append(job_config)
            self._register_job(job_config)
            
            logger.info(f"已添加新任务: {job_config.get('name', 'unknown')}")
            
        except Exception as e:
            logger.error(f"添加任务失败: {str(e)}")
    
    def remove_job(self, job_name: str):
        """移除任务"""
        try:
            # 从配置中移除
            self.jobs = [job for job in self.jobs if job.get('name') != job_name]
            
            # 重新注册所有任务
            schedule.clear()
            for job_config in self.jobs:
                self._register_job(job_config)
            
            logger.info(f"已移除任务: {job_name}")
            
        except Exception as e:
            logger.error(f"移除任务失败: {str(e)}")
    
    def get_next_run_times(self) -> List[Dict]:
        """获取下次运行时间"""
        next_runs = []
        
        for job in schedule.jobs:
            next_runs.append({
                'job': str(job.job_func),
                'next_run': job.next_run.isoformat() if job.next_run else None
            })
        
        return next_runs
    
    def get_scheduler_status(self) -> Dict:
        """获取调度器状态"""
        return {
            'running': self.running,
            'timezone': self.timezone,
            'jobs_count': len(self.jobs),
            'registered_jobs': len(schedule.jobs),
            'next_runs': self.get_next_run_times()
        }
