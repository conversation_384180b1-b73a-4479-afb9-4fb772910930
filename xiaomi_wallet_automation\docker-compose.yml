version: '3.8'

services:
  xiaomi-automation:
    build: .
    container_name: xiaomi-wallet-automation
    restart: unless-stopped
    
    environment:
      - TZ=Asia/Shanghai
      - PYTHONUNBUFFERED=1
      
    volumes:
      # 配置文件
      - ./config:/app/config
      # 数据持久化
      - ./data:/app/data
      # 日志文件
      - ./logs:/app/logs
      # 临时文件
      - xiaomi_temp:/app/temp
      - xiaomi_downloads:/app/downloads
      - xiaomi_screenshots:/app/screenshots
      
    # 网络模式
    network_mode: bridge
    
    # 资源限制
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 512M
          cpus: '0.5'
    
    # 健康检查
    healthcheck:
      test: ["CMD", "python", "-c", "import os; exit(0 if os.path.exists('/app/logs/main.log') else 1)"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    
    # 依赖服务（如果需要）
    # depends_on:
    #   - redis
    #   - mysql

  # Redis服务（可选，用于缓存）
  # redis:
  #   image: redis:7-alpine
  #   container_name: xiaomi-redis
  #   restart: unless-stopped
  #   volumes:
  #     - xiaomi_redis_data:/data
  #   command: redis-server --appendonly yes

  # MySQL服务（可选，用于数据存储）
  # mysql:
  #   image: mysql:8.0
  #   container_name: xiaomi-mysql
  #   restart: unless-stopped
  #   environment:
  #     MYSQL_ROOT_PASSWORD: xiaomi_root_password
  #     MYSQL_DATABASE: xiaomi_automation
  #     MYSQL_USER: xiaomi_user
  #     MYSQL_PASSWORD: xiaomi_password
  #   volumes:
  #     - xiaomi_mysql_data:/var/lib/mysql
  #   ports:
  #     - "3306:3306"

  # Web管理界面（可选）
  # web-ui:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.web
  #   container_name: xiaomi-web-ui
  #   restart: unless-stopped
  #   ports:
  #     - "8080:8080"
  #   volumes:
  #     - ./config:/app/config:ro
  #     - ./data:/app/data:ro
  #     - ./logs:/app/logs:ro
  #   depends_on:
  #     - xiaomi-automation

volumes:
  xiaomi_temp:
    driver: local
  xiaomi_downloads:
    driver: local
  xiaomi_screenshots:
    driver: local
  # xiaomi_redis_data:
  #   driver: local
  # xiaomi_mysql_data:
  #   driver: local

networks:
  default:
    name: xiaomi-automation-network
