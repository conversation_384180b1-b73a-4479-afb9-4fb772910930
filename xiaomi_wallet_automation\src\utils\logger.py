"""
日志管理工具

提供统一的日志记录功能，支持文件输出、格式化、日志轮转等功能。
"""

import os
import sys
from pathlib import Path
from typing import Dict, Optional
from loguru import logger

# 全局日志配置
_logger_configured = False


def setup_logging(config: Dict) -> None:
    """设置日志配置"""
    global _logger_configured
    
    if _logger_configured:
        return
    
    # 移除默认的日志处理器
    logger.remove()
    
    # 获取日志配置
    log_config = config.get('logging', {})
    log_level = log_config.get('level', 'INFO')
    log_format = log_config.get('format', 
        "{time:YYYY-MM-DD HH:mm:ss} | {level} | {name}:{function}:{line} | {message}")
    rotation = log_config.get('rotation', '1 day')
    retention = log_config.get('retention', '30 days')
    compression = log_config.get('compression', 'zip')
    
    # 确保日志目录存在
    log_dir = Path('logs')
    log_dir.mkdir(exist_ok=True)
    
    # 控制台输出
    logger.add(
        sys.stdout,
        level=log_level,
        format=log_format,
        colorize=True
    )
    
    # 主日志文件
    main_log_file = log_config.get('files', {}).get('main', 'logs/main.log')
    logger.add(
        main_log_file,
        level=log_level,
        format=log_format,
        rotation=rotation,
        retention=retention,
        compression=compression,
        encoding='utf-8'
    )
    
    # 错误日志文件
    error_log_file = log_config.get('files', {}).get('error', 'logs/error.log')
    logger.add(
        error_log_file,
        level='ERROR',
        format=log_format,
        rotation=rotation,
        retention=retention,
        compression=compression,
        encoding='utf-8'
    )
    
    # 任务日志文件
    task_log_file = log_config.get('files', {}).get('task', 'logs/task.log')
    logger.add(
        task_log_file,
        level='INFO',
        format=log_format,
        rotation=rotation,
        retention=retention,
        compression=compression,
        encoding='utf-8',
        filter=lambda record: 'task' in record['name'].lower()
    )
    
    # 通知日志文件
    notification_log_file = log_config.get('files', {}).get('notification', 'logs/notification.log')
    logger.add(
        notification_log_file,
        level='INFO',
        format=log_format,
        rotation=rotation,
        retention=retention,
        compression=compression,
        encoding='utf-8',
        filter=lambda record: 'notification' in record['name'].lower()
    )
    
    _logger_configured = True
    logger.info("日志系统初始化完成")


def get_logger(name: str) -> logger:
    """获取指定名称的日志记录器"""
    return logger.bind(name=name)


class TaskLogger:
    """任务专用日志记录器"""
    
    def __init__(self, task_name: str, account_id: str):
        self.task_name = task_name
        self.account_id = account_id
        self.logger = get_logger(f"task.{task_name}")
        
    def info(self, message: str):
        """记录信息日志"""
        self.logger.info(f"[{self.account_id}] {message}")
        
    def warning(self, message: str):
        """记录警告日志"""
        self.logger.warning(f"[{self.account_id}] {message}")
        
    def error(self, message: str):
        """记录错误日志"""
        self.logger.error(f"[{self.account_id}] {message}")
        
    def success(self, message: str):
        """记录成功日志"""
        self.logger.success(f"[{self.account_id}] {message}")
        
    def debug(self, message: str):
        """记录调试日志"""
        self.logger.debug(f"[{self.account_id}] {message}")


class NotificationLogger:
    """通知专用日志记录器"""
    
    def __init__(self):
        self.logger = get_logger("notification")
        
    def info(self, service: str, message: str):
        """记录通知信息"""
        self.logger.info(f"[{service}] {message}")
        
    def error(self, service: str, message: str):
        """记录通知错误"""
        self.logger.error(f"[{service}] {message}")
        
    def success(self, service: str, message: str):
        """记录通知成功"""
        self.logger.success(f"[{service}] {message}")


def log_function_call(func):
    """函数调用日志装饰器"""
    def wrapper(*args, **kwargs):
        func_logger = get_logger(f"{func.__module__}.{func.__name__}")
        func_logger.debug(f"调用函数: {func.__name__}, 参数: args={args}, kwargs={kwargs}")
        
        try:
            result = func(*args, **kwargs)
            func_logger.debug(f"函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            func_logger.error(f"函数 {func.__name__} 执行失败: {str(e)}")
            raise
            
    return wrapper


def log_async_function_call(func):
    """异步函数调用日志装饰器"""
    async def wrapper(*args, **kwargs):
        func_logger = get_logger(f"{func.__module__}.{func.__name__}")
        func_logger.debug(f"调用异步函数: {func.__name__}, 参数: args={args}, kwargs={kwargs}")
        
        try:
            result = await func(*args, **kwargs)
            func_logger.debug(f"异步函数 {func.__name__} 执行成功")
            return result
        except Exception as e:
            func_logger.error(f"异步函数 {func.__name__} 执行失败: {str(e)}")
            raise
            
    return wrapper


class LogContext:
    """日志上下文管理器"""
    
    def __init__(self, logger_name: str, context_info: str):
        self.logger = get_logger(logger_name)
        self.context_info = context_info
        
    def __enter__(self):
        self.logger.info(f"开始 {self.context_info}")
        return self
        
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is None:
            self.logger.info(f"完成 {self.context_info}")
        else:
            self.logger.error(f"失败 {self.context_info}: {exc_val}")


def create_log_file_if_not_exists(file_path: str):
    """如果日志文件不存在则创建"""
    log_file = Path(file_path)
    log_file.parent.mkdir(parents=True, exist_ok=True)
    
    if not log_file.exists():
        log_file.touch()


def get_log_file_size(file_path: str) -> int:
    """获取日志文件大小（字节）"""
    try:
        return Path(file_path).stat().st_size
    except FileNotFoundError:
        return 0


def clean_old_logs(log_dir: str = 'logs', days: int = 30):
    """清理旧的日志文件"""
    log_path = Path(log_dir)
    if not log_path.exists():
        return
        
    import time
    current_time = time.time()
    cutoff_time = current_time - (days * 24 * 60 * 60)
    
    for log_file in log_path.glob('*.log*'):
        if log_file.stat().st_mtime < cutoff_time:
            try:
                log_file.unlink()
                logger.info(f"删除旧日志文件: {log_file}")
            except Exception as e:
                logger.error(f"删除日志文件失败 {log_file}: {str(e)}")


# 导出主要的日志函数
__all__ = [
    'setup_logging',
    'get_logger', 
    'TaskLogger',
    'NotificationLogger',
    'log_function_call',
    'log_async_function_call',
    'LogContext',
    'create_log_file_if_not_exists',
    'get_log_file_size',
    'clean_old_logs'
]
